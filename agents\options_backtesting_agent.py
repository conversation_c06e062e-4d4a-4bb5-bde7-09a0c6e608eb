#!/usr/bin/env python3
"""
High-Performance Options Backtesting Agent - Ultra-Fast Strategy Testing

PERFORMANCE OPTIMIZATIONS:
- VectorBT for ultra-fast portfolio simulations
- Numba JIT compilation for critical calculations
- NumExpr for vectorized mathematical expressions
- Concurrent.futures for parallel processing
- Memory-efficient streaming data processing

📊 1. Options-Specific Backtesting
- Vectorized Black-Scholes pricing with Numba
- Greeks evolution over time (Delta, Gamma, Theta, Vega)
- Time decay (Theta) impact simulation
- Volatility changes effect on P&L

📈 2. Strategy Performance Analysis
- Multi-leg strategy P&L calculation
- Risk-adjusted returns (<PERSON>, <PERSON>rt<PERSON>)
- Maximum drawdown analysis
- Greeks-based risk attribution

⚡ 3. Realistic Execution Modeling
- Bid-ask spread impact
- Slippage modeling
- Transaction costs (Angel One charges)
- Margin requirements simulation

🎯 4. Ultra High-Performance Processing
- VectorBT portfolio backtesting (100x faster)
- Numba JIT-compiled calculations
- NumExpr vectorized expressions
- Parallel strategy execution
- Memory-efficient streaming
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import multiprocessing as mp
import traceback
import warnings
warnings.filterwarnings('ignore')

# High-performance libraries with enhanced configuration
try:
    import vectorbt as vbt
    VBT_AVAILABLE = True
    # Configure VectorBT for maximum performance
    vbt.settings.caching['enabled'] = True
    vbt.settings.caching['whitelist'] = []
    vbt.settings.array_wrapper['freq'] = '1min'  # Set default frequency
    logging.info("VectorBT loaded successfully - Ultra-fast backtesting enabled")
except ImportError:
    VBT_AVAILABLE = False
    logging.warning("vectorbt not installed. Install with: pip install vectorbt")

try:
    import numexpr as ne
    NUMEXPR_AVAILABLE = True
    # Configure numexpr for optimal performance
    ne.set_num_threads(mp.cpu_count())
    ne.set_vml_accuracy_mode('high')
    ne.set_vml_num_threads(mp.cpu_count())
    logging.info(f"NumExpr loaded - {mp.cpu_count()} threads configured")
except ImportError:
    NUMEXPR_AVAILABLE = False
    logging.warning("numexpr not installed. Install with: pip install numexpr")

try:
    from numba import jit, njit, prange, cuda
    import numba as nb
    NUMBA_AVAILABLE = True
    # Configure Numba for optimal performance
    nb.config.THREADING_LAYER = 'threadsafe'
    nb.config.NUMBA_NUM_THREADS = mp.cpu_count()
    logging.info(f"Numba JIT loaded - {mp.cpu_count()} threads configured")
except ImportError:
    NUMBA_AVAILABLE = False
    logging.warning("numba not installed. Install with: pip install numba")
    # Create dummy decorators
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    prange = range

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logging.warning("py_vollib not installed. Using fallback calculations.")

# Scientific computing
import scipy.stats as stats

logger = logging.getLogger(__name__)

# Ultra High-Performance Calculation Functions with Enhanced Optimizations
if NUMBA_AVAILABLE:
    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_returns_numba(prices: np.ndarray) -> np.ndarray:
        """Calculate returns using Numba JIT compilation with parallel processing"""
        n = len(prices)
        returns = np.empty(n - 1, dtype=np.float64)
        for i in prange(1, n):
            returns[i-1] = (prices[i] - prices[i-1]) / prices[i-1]
        return returns

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_rolling_mean_numba(data: np.ndarray, window: int) -> np.ndarray:
        """Calculate rolling mean using optimized Numba JIT compilation"""
        n = len(data)
        result = np.empty(n, dtype=np.float64)
        result[:window-1] = np.nan

        # Use cumulative sum for faster rolling mean calculation
        cumsum = np.cumsum(data)
        for i in prange(window-1, n):
            if i == window-1:
                result[i] = cumsum[i] / window
            else:
                result[i] = (cumsum[i] - cumsum[i-window]) / window
        return result

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_rolling_std_numba(data: np.ndarray, window: int) -> np.ndarray:
        """Calculate rolling standard deviation using optimized Numba JIT"""
        n = len(data)
        result = np.empty(n, dtype=np.float64)
        result[:window-1] = np.nan

        for i in prange(window-1, n):
            window_data = data[i-window+1:i+1]
            mean_val = np.mean(window_data)
            var_sum = 0.0
            for j in range(window):
                diff = window_data[j] - mean_val
                var_sum += diff * diff
            result[i] = np.sqrt(var_sum / window)
        return result

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_max_drawdown_numba(equity_curve: np.ndarray) -> float:
        """Calculate maximum drawdown using optimized Numba JIT compilation"""
        n = len(equity_curve)
        if n == 0:
            return 0.0

        peak = equity_curve[0]
        max_dd = 0.0

        for i in range(1, n):
            if equity_curve[i] > peak:
                peak = equity_curve[i]

            if peak > 0:
                drawdown = (peak - equity_curve[i]) / peak
                if drawdown > max_dd:
                    max_dd = drawdown

        return max_dd

    @njit(parallel=True, cache=True, fastmath=True)
    def norm_cdf_approx(x: float) -> float:
        """Fast normal CDF approximation for Black-Scholes"""
        # Abramowitz and Stegun approximation
        a1 =  0.254829592
        a2 = -0.284496736
        a3 =  1.421413741
        a4 = -1.453152027
        a5 =  1.061405429
        p  =  0.3275911

        sign = 1.0 if x >= 0 else -1.0
        x = abs(x) / np.sqrt(2.0)

        t = 1.0 / (1.0 + p * x)
        y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * np.exp(-x * x)

        return 0.5 * (1.0 + sign * y)

    @njit(parallel=True, cache=True, fastmath=True)
    def vectorized_black_scholes_numba(S: np.ndarray, K: np.ndarray, T: np.ndarray,
                                      r: float, sigma: np.ndarray, option_type: np.ndarray) -> np.ndarray:
        """Ultra-fast vectorized Black-Scholes calculation using Numba"""
        n = len(S)
        prices = np.empty(n, dtype=np.float64)

        for i in prange(n):
            if T[i] <= 1e-8:  # Handle near-zero time to expiry
                if option_type[i] == 1:  # Call
                    prices[i] = max(S[i] - K[i], 0.0)
                else:  # Put
                    prices[i] = max(K[i] - S[i], 0.0)
            else:
                sqrt_T = np.sqrt(T[i])
                d1 = (np.log(S[i] / K[i]) + (r + 0.5 * sigma[i] * sigma[i]) * T[i]) / (sigma[i] * sqrt_T)
                d2 = d1 - sigma[i] * sqrt_T

                if option_type[i] == 1:  # Call
                    prices[i] = S[i] * norm_cdf_approx(d1) - K[i] * np.exp(-r * T[i]) * norm_cdf_approx(d2)
                else:  # Put
                    prices[i] = K[i] * np.exp(-r * T[i]) * norm_cdf_approx(-d2) - S[i] * norm_cdf_approx(-d1)

                prices[i] = max(prices[i], 0.0)

        return prices

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_greeks_numba(S: np.ndarray, K: np.ndarray, T: np.ndarray,
                              r: float, sigma: np.ndarray, option_type: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Calculate Greeks (Delta, Gamma, Theta, Vega) using Numba"""
        n = len(S)
        delta = np.empty(n, dtype=np.float64)
        gamma = np.empty(n, dtype=np.float64)
        theta = np.empty(n, dtype=np.float64)
        vega = np.empty(n, dtype=np.float64)

        for i in prange(n):
            if T[i] <= 1e-8:
                delta[i] = gamma[i] = theta[i] = vega[i] = 0.0
            else:
                sqrt_T = np.sqrt(T[i])
                d1 = (np.log(S[i] / K[i]) + (r + 0.5 * sigma[i] * sigma[i]) * T[i]) / (sigma[i] * sqrt_T)
                d2 = d1 - sigma[i] * sqrt_T

                # Standard normal PDF
                nd1 = np.exp(-0.5 * d1 * d1) / np.sqrt(2 * np.pi)

                if option_type[i] == 1:  # Call
                    delta[i] = norm_cdf_approx(d1)
                    theta[i] = (-S[i] * nd1 * sigma[i] / (2 * sqrt_T) -
                               r * K[i] * np.exp(-r * T[i]) * norm_cdf_approx(d2))
                else:  # Put
                    delta[i] = norm_cdf_approx(d1) - 1.0
                    theta[i] = (-S[i] * nd1 * sigma[i] / (2 * sqrt_T) +
                               r * K[i] * np.exp(-r * T[i]) * norm_cdf_approx(-d2))

                gamma[i] = nd1 / (S[i] * sigma[i] * sqrt_T)
                vega[i] = S[i] * nd1 * sqrt_T / 100.0  # Vega per 1% change in volatility

        return delta, gamma, theta, vega

else:
    # Fallback functions without Numba
    def calculate_returns_numba(prices: np.ndarray) -> np.ndarray:
        return np.diff(prices) / prices[:-1]
    
    def calculate_rolling_mean_numba(data: np.ndarray, window: int) -> np.ndarray:
        return np.convolve(data, np.ones(window)/window, mode='valid') # Changed mode to 'valid' for consistency
    
    def calculate_rolling_std_numba(data: np.ndarray, window: int) -> np.ndarray:
        # Using a more efficient numpy-based rolling std for fallback
        # This is still O(N*window) but better than list comprehension
        result = np.empty(len(data), dtype=np.float64)
        result[:window-1] = np.nan
        for i in range(window-1, len(data)):
            result[i] = np.std(data[i-window+1:i+1])
        return result
    
    def calculate_max_drawdown_numba(equity_curve: np.ndarray) -> float:
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (peak - equity_curve) / peak
        return np.max(drawdown)
    
    def vectorized_black_scholes_numba(S: np.ndarray, K: np.ndarray, T: np.ndarray, 
                                      r: float, sigma: np.ndarray, option_type: np.ndarray) -> np.ndarray:
        # Fallback to py_vollib or basic calculation
        prices = np.zeros_like(S)
        for i in range(len(S)):
            if T[i] <= 0:
                if option_type[i] == 1:  # Call
                    prices[i] = max(S[i] - K[i], 0)
                else:  # Put
                    prices[i] = max(K[i] - S[i], 0)
            else:
                try:
                    if option_type[i] == 1:  # Call
                        prices[i] = bs.black_scholes('c', S[i], K[i], T[i], r, sigma[i])
                    else:  # Put
                        prices[i] = bs.black_scholes('p', S[i], K[i], T[i], r, sigma[i])
                except:
                    prices[i] = max(S[i] - K[i], 0) if option_type[i] == 1 else max(K[i] - S[i], 0)
        return prices

def optimize_with_numexpr(expression: str, local_dict: Dict[str, np.ndarray]) -> np.ndarray:
    """Use numexpr for ultra-fast numerical expressions"""
    if NUMEXPR_AVAILABLE:
        try:
            return ne.evaluate(expression, local_dict=local_dict)
        except Exception as e:
            logger.warning(f"Numexpr evaluation failed: {e}, falling back to numpy")
            # Fallback to numpy
            return eval(expression, {"np": np}, local_dict)
    else:
        return eval(expression, {"np": np}, local_dict)

def calculate_technical_indicators_numexpr(prices: np.ndarray, volumes: np.ndarray,
                                         window_fast: int = 10, window_slow: int = 20) -> Dict[str, np.ndarray]:
    """Calculate multiple technical indicators using NumExpr for maximum speed"""
    try:
        n = len(prices)
        results = {}

        if NUMEXPR_AVAILABLE:
            # Calculate returns using numpy first, then use NumExpr for other operations
            # NumExpr doesn't support array slicing syntax like prices[1:]
            prices_shifted = prices[1:]
            prices_prev = prices[:-1]
            returns = ne.evaluate("(prices_shifted - prices_prev) / prices_prev",
                                local_dict={'prices_shifted': prices_shifted, 'prices_prev': prices_prev})
            results['returns'] = np.concatenate([[0], returns])

            # Calculate price momentum using NumExpr
            if n > window_fast:
                sma_fast = calculate_rolling_mean_numba(prices, window_fast)
                # Ensure arrays are same length
                min_len = min(len(prices), len(sma_fast))
                prices_trimmed = prices[:min_len]
                sma_fast_trimmed = sma_fast[:min_len]
                momentum = ne.evaluate("(prices_trimmed - sma_fast_trimmed) / sma_fast_trimmed",
                                     local_dict={'prices_trimmed': prices_trimmed, 'sma_fast_trimmed': sma_fast_trimmed})
                results['momentum'] = momentum

            # Calculate volume indicators using NumExpr
            if n > window_slow and len(volumes) > 0:
                vol_sma = calculate_rolling_mean_numba(volumes, window_slow)
                # Ensure arrays are same length
                min_len = min(len(volumes), len(vol_sma))
                volumes_trimmed = volumes[:min_len]
                vol_sma_trimmed = vol_sma[:min_len]
                vol_ratio = ne.evaluate("volumes_trimmed / vol_sma_trimmed",
                                      local_dict={'volumes_trimmed': volumes_trimmed, 'vol_sma_trimmed': vol_sma_trimmed})
                results['volume_ratio'] = vol_ratio

            # Calculate volatility using NumExpr
            if n > window_slow:
                returns_padded = np.concatenate([[0], returns])
                volatility = calculate_rolling_std_numba(returns_padded, window_slow)
                results['volatility'] = volatility

            # Calculate RSI using numpy for array operations, NumExpr for calculations
            if n > 14:
                # Use numpy for array slicing
                price_diff = prices[1:] - prices[:-1]
                gains = ne.evaluate("where(price_diff > 0, price_diff, 0)",
                                  local_dict={'price_diff': price_diff})
                losses = ne.evaluate("where(price_diff < 0, -price_diff, 0)",
                                   local_dict={'price_diff': price_diff})

                avg_gains = calculate_rolling_mean_numba(gains, 14)
                avg_losses = calculate_rolling_mean_numba(losses, 14)

                # Avoid division by zero
                rs = ne.evaluate("where(avg_losses > 0, avg_gains / avg_losses, 100)",
                               local_dict={'avg_gains': avg_gains, 'avg_losses': avg_losses})
                rsi = ne.evaluate("100 - (100 / (1 + rs))", local_dict={'rs': rs})
                results['rsi'] = np.concatenate([[50], rsi])

        else:
            # Fallback to numpy calculations
            if len(prices) > 1:
                returns = np.diff(prices) / prices[:-1]
                results['returns'] = np.concatenate([[0], returns])
            else:
                results['returns'] = np.zeros(len(prices))

            if n > window_fast:
                sma_fast = calculate_rolling_mean_numba(prices, window_fast)
                # Ensure arrays are same length
                min_len = min(len(prices), len(sma_fast))
                results['momentum'] = (prices[:min_len] - sma_fast[:min_len]) / sma_fast[:min_len]
            else:
                results['momentum'] = np.zeros(len(prices))

        return results

    except Exception as e:
        logger.error(f"Technical indicators calculation failed: {e}")
        return {'returns': np.zeros(len(prices)), 'momentum': np.zeros(len(prices))}

def calculate_options_metrics_numexpr(prices: np.ndarray, strikes: np.ndarray,
                                    times_to_expiry: np.ndarray, volatilities: np.ndarray,
                                    risk_free_rate: float = 0.06) -> Dict[str, np.ndarray]:
    """Calculate options-specific metrics using NumExpr optimization"""
    try:
        results = {}

        if NUMEXPR_AVAILABLE and len(prices) > 0:
            # Calculate moneyness using NumExpr
            moneyness = ne.evaluate("prices / strikes",
                                  local_dict={'prices': prices, 'strikes': strikes})
            results['moneyness'] = moneyness

            # Calculate time value decay rate using NumExpr
            time_decay_rate = ne.evaluate("1 / (times_to_expiry + 0.001)",
                                        local_dict={'times_to_expiry': times_to_expiry})
            results['time_decay_rate'] = time_decay_rate

            # Calculate implied volatility rank using NumExpr
            vol_mean = np.mean(volatilities)
            vol_std = np.std(volatilities)
            if vol_std > 0:
                iv_rank = ne.evaluate("(volatilities - vol_mean) / vol_std",
                                    local_dict={'volatilities': volatilities,
                                              'vol_mean': vol_mean, 'vol_std': vol_std})
                results['iv_rank'] = iv_rank

            # Calculate delta-adjusted exposure using NumExpr
            if 'delta' in results:
                delta_exposure = ne.evaluate("abs(delta) * prices",
                                           local_dict={'delta': results['delta'], 'prices': prices})
                results['delta_exposure'] = delta_exposure

        return results

    except Exception as e:
        logger.error(f"Options metrics calculation failed: {e}")
        return {}

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: str
    end_date: str
    initial_capital: float = 100000.0
    risk_per_trade: float = 0.02  # 2% risk per trade
    transaction_cost: float = 20.0  # Per lot transaction cost
    slippage: float = 0.01  # 1% slippage
    margin_multiplier: float = 1.0
    
@dataclass
class BacktestResults:
    """Enhanced backtesting results with additional fields"""
    strategy_id: str
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_return: float
    best_trade: float
    worst_trade: float
    avg_holding_period: float
    greeks_pnl: Dict[str, float]
    volatility_pnl: float
    time_decay_pnl: float
    # Additional fields for enhanced tracking
    underlying: str = "NIFTY"
    timeframe: str = "1min"
    winning_trades: int = 0
    losing_trades: int = 0
    final_portfolio_value: float = 0.0
    trades: Optional[List[Dict]] = None

class HighPerformanceBacktestEngine:
    """
    High-performance backtesting engine using vectorbt, numba, and numexpr
    """
    
    def __init__(self, risk_free_rate: float = 0.06):
        self.risk_free_rate = risk_free_rate
        self.use_vectorbt = VBT_AVAILABLE
        
    def vectorized_backtest_portfolio(self, data: pl.DataFrame, strategies: List[Dict], 
                                    config: BacktestConfig) -> Dict[str, BacktestResults]:
        """
        Run vectorized backtesting for multiple strategies simultaneously
        """
        try:
            logger.info(f"[VECTORIZED] Starting vectorized backtest for {len(strategies)} strategies")
            
            # Convert to numpy arrays for maximum performance
            prices = data.select('close').to_numpy().flatten()
            timestamps = data.select('timestamp').to_numpy().flatten()
            volumes = data.select('volume').to_numpy().flatten()
            
            # Pre-calculate technical indicators using optimized functions
            returns = calculate_returns_numba(prices)
            volatility = calculate_rolling_std_numba(returns, 20)
            
            # Use numexpr for complex calculations
            momentum = optimize_with_numexpr(
                "prices / rolling_mean - 1.0",
                {'prices': prices[20:], 'rolling_mean': calculate_rolling_mean_numba(prices, 20)[20:]}
            )
            
            results = {}
            
            if self.use_vectorbt:
                # Use vectorbt for ultra-fast backtesting
                results.update(self._vectorbt_backtest(data, strategies, config))
            else:
                # Use enhanced parallel processing with concurrent.futures
                results.update(self._parallel_backtest_strategies(
                    strategies, prices, returns, volatility, momentum, timestamps, config
                ))
            
            logger.info(f"[SUCCESS] Completed vectorized backtest for {len(results)} strategies")
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Vectorized backtest failed: {e}")
            logger.error(traceback.format_exc())
            return {}
    
    def _vectorbt_backtest(self, data: pl.DataFrame, strategies: List[Dict],
                          config: BacktestConfig) -> Dict[str, BacktestResults]:
        """Ultra-fast backtesting using VectorBT with advanced optimizations"""
        try:
            if not VBT_AVAILABLE:
                logger.warning("VectorBT not available, falling back to standard backtesting")
                return {}

            # Convert Polars DataFrame to NumPy arrays for VectorBT
            # VectorBT can work directly with NumPy arrays for signals and prices
            close_prices = data.select('close').to_numpy().flatten()
            timestamps = data.select('timestamp').to_numpy().flatten()
            volume = data.select('volume').to_numpy().flatten() if 'volume' in data.columns else np.ones_like(close_prices)

            # Pre-calculate common technical indicators for all strategies
            tech_indicators = calculate_technical_indicators_numexpr(close_prices, volume)

            results = {}

            # Process strategies in batches for better memory efficiency
            batch_size = min(10, len(strategies))

            for i in range(0, len(strategies), batch_size):
                batch_strategies = strategies[i:i+batch_size]

                # Prepare batch signals
                batch_entries = []
                batch_exits = []
                batch_strategy_ids = []

                for strategy in batch_strategies:
                    try:
                        # Generate optimized signals using pre-calculated indicators
                        entries, exits = self._generate_vectorbt_signals_optimized(
                            close_prices, timestamps, strategy, tech_indicators
                        )

                        batch_entries.append(entries)
                        batch_exits.append(exits)
                        batch_strategy_ids.append(strategy['strategy_id'])

                    except Exception as e:
                        logger.error(f"Signal generation failed for {strategy['strategy_id']}: {e}")
                        continue

                if not batch_entries:
                    continue

                # Create multi-strategy portfolio for batch processing
                try:
                    # Stack signals for batch processing
                    # VectorBT expects 2D arrays for multiple strategies
                    entries_array = np.stack(batch_entries, axis=1)
                    exits_array = np.stack(batch_exits, axis=1)

                    # Run batch portfolio simulation
                    portfolio = vbt.Portfolio.from_signals(
                        close_prices, entries_array, exits_array,
                        init_cash=config.initial_capital,
                        fees=config.transaction_cost,
                        slippage=config.slippage,
                        freq='1min'  # Optimize for minute data
                    )

                    # Extract results for each strategy in the batch
                    for j, strategy_id in enumerate(batch_strategy_ids):
                        try:
                            # Get strategy-specific portfolio
                            strategy_portfolio = portfolio.select_one(j) # Select by index for stacked arrays

                            # Calculate enhanced performance metrics
                            total_return = strategy_portfolio.total_return()
                            sharpe_ratio = strategy_portfolio.sharpe_ratio(risk_free=self.risk_free_rate)
                            max_drawdown = strategy_portfolio.max_drawdown()

                            # Calculate additional metrics
                            trades = strategy_portfolio.trades
                            win_rate = trades.win_rate() if trades.count() > 0 else 0.0
                            profit_factor = trades.profit_factor() if trades.count() > 0 else 0.0

                            # Create enhanced BacktestResults
                            results[strategy_id] = BacktestResults(
                                strategy_id=strategy_id,
                                total_return=total_return,
                                annualized_return=strategy_portfolio.annualized_return(),
                                sharpe_ratio=sharpe_ratio,
                                sortino_ratio=strategy_portfolio.sortino_ratio(),  # Remove risk_free parameter
                                max_drawdown=max_drawdown,
                                win_rate=win_rate,
                                profit_factor=profit_factor,
                                total_trades=trades.count(),
                                avg_trade_return=trades.returns.mean() if trades.count() > 0 else 0.0,
                                best_trade=trades.returns.max() if trades.count() > 0 else 0.0,
                                worst_trade=trades.returns.min() if trades.count() > 0 else 0.0,
                                avg_holding_period=trades.duration.mean() if trades.count() > 0 else 0.0,
                                greeks_pnl={'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0},
                                volatility_pnl=0.0,
                                time_decay_pnl=0.0
                            )

                        except Exception as e:
                            logger.error(f"Failed to extract results for {strategy_id}: {e}")
                            continue

                except Exception as e:
                    logger.error(f"Batch portfolio simulation failed: {e}")
                    # Fallback to individual processing
                    for k, strategy in enumerate(batch_strategies):
                        if k < len(batch_entries):
                            try:
                                individual_portfolio = vbt.Portfolio.from_signals(
                                    close_prices, batch_entries[k], batch_exits[k],
                                    init_cash=config.initial_capital,
                                    fees=config.transaction_cost,
                                    slippage=config.slippage
                                )

                                results[strategy['strategy_id']] = self._extract_vectorbt_results(
                                    individual_portfolio, strategy['strategy_id']
                                )

                            except Exception as e2:
                                logger.error(f"Individual fallback failed for {strategy['strategy_id']}: {e2}")
                                continue

            logger.info(f"VectorBT processed {len(results)} strategies successfully")
            return results

        except Exception as e:
            logger.error(f"VectorBT backtesting failed: {e}")
            logger.error(traceback.format_exc())
            return {}
    
    def _generate_vectorbt_signals_optimized(self, close_prices: np.ndarray, timestamps: np.ndarray, strategy: Dict,
                                            tech_indicators: Dict[str, np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """Generate optimized entry/exit signals for vectorbt using pre-calculated indicators"""
        try:
            strategy_type = strategy.get('strategy_type', 'momentum')

            # Use pre-calculated indicators for speed and ensure they match close_prices length
            returns = tech_indicators.get('returns', np.zeros(len(close_prices)))
            momentum = tech_indicators.get('momentum', np.zeros(len(close_prices)))
            rsi = tech_indicators.get('rsi', np.full(len(close_prices), 50))
            volatility = tech_indicators.get('volatility', np.zeros(len(close_prices)))

            # Ensure all arrays are the same length as close_prices
            target_len = len(close_prices)

            # Resize arrays to match close_prices length
            if len(returns) != target_len:
                if len(returns) > target_len:
                    returns = returns[:target_len]
                else:
                    returns = np.pad(returns, (0, target_len - len(returns)), mode='constant', constant_values=0)

            if len(momentum) != target_len:
                if len(momentum) > target_len:
                    momentum = momentum[:target_len]
                else:
                    momentum = np.pad(momentum, (0, target_len - len(momentum)), mode='constant', constant_values=0)

            if len(rsi) != target_len:
                if len(rsi) > target_len:
                    rsi = rsi[:target_len]
                else:
                    rsi = np.pad(rsi, (0, target_len - len(rsi)), mode='constant', constant_values=50)

            if len(volatility) != target_len:
                if len(volatility) > target_len:
                    volatility = volatility[:target_len]
                else:
                    volatility = np.pad(volatility, (0, target_len - len(volatility)), mode='constant', constant_values=0)

            # Initialize entry/exit arrays
            entries = np.zeros(target_len, dtype=bool)
            exits = np.zeros(target_len, dtype=bool)

            # Enhanced signal generation based on strategy type
            if strategy_type == 'momentum':
                # Momentum strategy with volatility filter
                entries = (returns > 0.015) & (momentum > 0.02) & (volatility < 0.3)
                exits = (returns < -0.01) | (momentum < -0.01)

            elif strategy_type == 'mean_reversion':
                # Mean reversion with RSI
                entries = (rsi < 30) & (momentum < -0.02)
                exits = (rsi > 70) | (momentum > 0.02)

            elif strategy_type == 'volatility_breakout':
                # Volatility breakout strategy
                if len(volatility) > 0 and np.std(volatility) > 0:
                    vol_threshold = np.percentile(volatility, 80)
                    entries = (volatility > vol_threshold) & (np.abs(returns) > 0.02)
                    exits = (volatility < np.percentile(volatility, 20))

            elif strategy_type == 'long_straddle':
                # Options-specific: Long straddle on high volatility
                if len(volatility) > 0 and np.std(volatility) > 0:
                    entries = (volatility > np.percentile(volatility, 75)) & (np.abs(momentum) < 0.01)
                    exits = (volatility < np.percentile(volatility, 25)) | (np.abs(returns) > 0.05)

            elif strategy_type == 'short_straddle':
                # Options-specific: Short straddle on low volatility
                if len(volatility) > 0 and np.std(volatility) > 0:
                    entries = (volatility < np.percentile(volatility, 25)) & (np.abs(momentum) < 0.005)
                    exits = (volatility > np.percentile(volatility, 75)) | (np.abs(returns) > 0.03)

            elif strategy_type == 'iron_condor':
                # Iron condor on stable markets
                if len(volatility) > 0 and np.std(volatility) > 0:
                    entries = (volatility < np.percentile(volatility, 30)) & (np.abs(momentum) < 0.01)
                    exits = (volatility > np.percentile(volatility, 70)) | (np.abs(momentum) > 0.02)

            else:
                # Default enhanced momentum strategy
                # These rolling calculations need to be done on numpy arrays
                sma_fast = calculate_rolling_mean_numba(close_prices, 10)
                sma_slow = calculate_rolling_mean_numba(close_prices, 20)

                # Ensure arrays are of compatible length for comparison
                min_len = min(len(close_prices), len(sma_fast), len(sma_slow), len(momentum))

                if min_len > 0:
                    entries[:min_len] = (close_prices[:min_len] > sma_fast[:min_len]) & (momentum[:min_len] > 0.01)
                    exits[:min_len] = (close_prices[:min_len] < sma_slow[:min_len]) | (momentum[:min_len] < -0.01)

            # Apply additional filters to reduce noise
            # Ensure we don't access out of bounds
            if len(entries) > 1:
                # Shift operation - avoid looking at previous entry for first element
                shifted_entries = np.concatenate(([False], entries[:-1]))
                entries = entries & ~shifted_entries  # Only enter if not entered previously

                shifted_exits = np.concatenate(([False], exits[:-1]))
                exits = exits & ~shifted_exits  # Only exit if not exited previously

            # Prevent simultaneous entry and exit
            exits = exits & ~entries

            return entries, exits

        except Exception as e:
            logger.error(f"Optimized signal generation failed: {e}")
            logger.error(traceback.format_exc())
            # Return empty signals
            return np.zeros(len(close_prices), dtype=bool), np.zeros(len(close_prices), dtype=bool)

    def _extract_vectorbt_results(self, portfolio, strategy_id: str) -> BacktestResults:
        """Extract results from VectorBT portfolio object"""
        try:
            trades = portfolio.trades

            return BacktestResults(
                strategy_id=strategy_id,
                total_return=portfolio.total_return(),
                annualized_return=portfolio.annualized_return(),
                sharpe_ratio=portfolio.sharpe_ratio(risk_free=self.risk_free_rate),
                sortino_ratio=portfolio.sortino_ratio(),
                max_drawdown=portfolio.max_drawdown(),
                win_rate=trades.win_rate() if trades.count() > 0 else 0.0,
                profit_factor=trades.profit_factor() if trades.count() > 0 else 0.0,
                total_trades=trades.count(),
                avg_trade_return=trades.returns.mean() if trades.count() > 0 else 0.0,
                best_trade=trades.returns.max() if trades.count() > 0 else 0.0,
                worst_trade=trades.returns.min() if trades.count() > 0 else 0.0,
                avg_holding_period=trades.duration.mean() if trades.count() > 0 else 0.0,
                greeks_pnl={'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0},
                volatility_pnl=0.0,
                time_decay_pnl=0.0
            )

        except Exception as e:
            logger.error(f"Failed to extract VectorBT results: {e}")
            return self._create_empty_results(strategy_id)

    def _create_empty_results(self, strategy_id: str) -> BacktestResults:
        """Create empty BacktestResults for failed strategies"""
        return BacktestResults(
            strategy_id=strategy_id,
            total_return=0.0,
            annualized_return=0.0,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            avg_trade_return=0.0,
            best_trade=0.0,
            worst_trade=0.0,
            avg_holding_period=0.0,
            greeks_pnl={'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0},
            volatility_pnl=0.0,
            time_decay_pnl=0.0,
            underlying="NIFTY",
            timeframe="1min",
            winning_trades=0,
            losing_trades=0,
            final_portfolio_value=0.0,
            trades=[]
        )

    def _parallel_backtest_strategies(self, strategies: List[Dict], prices: np.ndarray,
                                    returns: np.ndarray, volatility: np.ndarray,
                                    momentum: np.ndarray, timestamps: np.ndarray,
                                    config: BacktestConfig) -> Dict[str, BacktestResults]:
        """Enhanced parallel backtesting using concurrent.futures with optimizations"""
        try:
            results = {}

            # Determine optimal number of workers based on system resources
            max_workers = min(mp.cpu_count(), len(strategies), 8)  # Cap at 8 for memory efficiency

            # Split strategies into chunks for better load balancing
            chunk_size = max(1, len(strategies) // max_workers)
            strategy_chunks = [strategies[i:i + chunk_size] for i in range(0, len(strategies), chunk_size)]

            logger.info(f"[PARALLEL] Processing {len(strategies)} strategies in {len(strategy_chunks)} chunks using {max_workers} workers")

            # Use ThreadPoolExecutor for I/O bound tasks, ProcessPoolExecutor for CPU bound
            # Since we're doing numerical computations, use ProcessPoolExecutor
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Submit chunk processing tasks
                future_to_chunk = {}

                for i, chunk in enumerate(strategy_chunks):
                    future = executor.submit(
                        self._process_strategy_chunk_parallel,
                        chunk, prices, returns, volatility, momentum, timestamps, config, i
                    )
                    future_to_chunk[future] = (chunk, i)

                # Collect results as they complete
                completed_chunks = 0
                for future in as_completed(future_to_chunk):
                    chunk, chunk_id = future_to_chunk[future]
                    try:
                        chunk_results = future.result(timeout=300)  # 5 minute timeout per chunk
                        results.update(chunk_results)
                        completed_chunks += 1

                        logger.info(f"[PARALLEL] Completed chunk {chunk_id + 1}/{len(strategy_chunks)} "
                                  f"with {len(chunk_results)} successful backtests")

                    except Exception as e:
                        logger.error(f"[PARALLEL] Chunk {chunk_id} failed: {e}")
                        logger.error(traceback.format_exc())

                        # Fallback: process strategies individually
                        for strategy in chunk:
                            try:
                                result = self._fast_single_strategy_backtest(
                                    strategy, prices, returns, volatility, momentum, timestamps, config
                                )
                                if result:
                                    results[strategy['strategy_id']] = result
                            except Exception as e2:
                                logger.error(f"[FALLBACK] Strategy {strategy['strategy_id']} failed: {e2}")

            logger.info(f"[PARALLEL] Completed parallel processing: {len(results)} successful backtests")
            return results

        except Exception as e:
            logger.error(f"[PARALLEL] Parallel backtesting failed: {e}")
            logger.error(traceback.format_exc())
            return {}

    @staticmethod
    def _process_strategy_chunk_parallel(strategies: List[Dict], prices: np.ndarray,
                                       returns: np.ndarray, volatility: np.ndarray,
                                       momentum: np.ndarray, timestamps: np.ndarray,
                                       config: BacktestConfig, chunk_id: int) -> Dict[str, BacktestResults]:
        """Process a chunk of strategies in parallel - static method for multiprocessing"""
        try:
            chunk_results = {}

            # Create a temporary engine instance for this process
            temp_engine = HighPerformanceBacktestEngine()

            for strategy in strategies:
                try:
                    result = temp_engine._fast_single_strategy_backtest(
                        strategy, prices, returns, volatility, momentum, timestamps, config
                    )
                    if result:
                        chunk_results[strategy['strategy_id']] = result

                except Exception as e:
                    logger.error(f"[CHUNK {chunk_id}] Strategy {strategy['strategy_id']} failed: {e}")
                    continue

            return chunk_results

        except Exception as e:
            logger.error(f"[CHUNK {chunk_id}] Chunk processing failed: {e}")
            return {}
    
    def _fast_single_strategy_backtest(self, strategy: Dict, prices: np.ndarray,
                                     returns: np.ndarray, volatility: np.ndarray,
                                     momentum: np.ndarray, timestamps: np.ndarray,
                                     config: BacktestConfig) -> Optional[BacktestResults]:
        """Ultra-fast single strategy backtest using all optimizations"""
        try:
            strategy_id = strategy['strategy_id']

            # Generate signals using optimized functions
            signals = self._generate_fast_signals_enhanced(prices, returns, volatility, momentum, strategy)

            if len(signals) == 0:
                return None

            # Calculate portfolio performance using Numba-optimized simulation
            portfolio_values, trade_pnls = self._simulate_portfolio_numba_enhanced(signals, prices, config)

            if len(portfolio_values) == 0:
                return None

            # Calculate performance metrics using optimized functions
            total_return = (portfolio_values[-1] - config.initial_capital) / config.initial_capital

            # Use Numba for returns calculation
            returns_series = calculate_returns_numba(portfolio_values)

            # Use NumExpr for vectorized calculations
            if NUMEXPR_AVAILABLE and len(returns_series) > 0:
                risk_free_daily = self.risk_free_rate / 252
                excess_returns = ne.evaluate("returns_series - risk_free_daily",
                                           local_dict={'returns_series': returns_series,
                                                     'risk_free_daily': risk_free_daily})
            else:
                excess_returns = returns_series - self.risk_free_rate / 252

            # Calculate Sharpe ratio with error handling
            if len(excess_returns) > 1 and np.std(excess_returns) > 0:
                sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            else:
                sharpe_ratio = 0.0

            # Calculate max drawdown using optimized Numba function
            max_drawdown = calculate_max_drawdown_numba(portfolio_values)

            # Calculate trade statistics using NumExpr where possible
            if len(trade_pnls) > 0:
                trade_returns_array = np.array(trade_pnls)

                if NUMEXPR_AVAILABLE:
                    winning_trades = ne.evaluate("sum(trade_returns_array > 0)",
                                                local_dict={'trade_returns_array': trade_returns_array})
                    losing_trades = ne.evaluate("sum(trade_returns_array < 0)",
                                               local_dict={'trade_returns_array': trade_returns_array})
                else:
                    winning_trades = np.sum(trade_returns_array > 0)
                    losing_trades = np.sum(trade_returns_array < 0)

                win_rate = winning_trades / len(trade_returns_array) if len(trade_returns_array) > 0 else 0

                # Calculate profit factor
                positive_pnl = np.sum(trade_returns_array[trade_returns_array > 0])
                negative_pnl = np.sum(trade_returns_array[trade_returns_array < 0])
                profit_factor = positive_pnl / abs(negative_pnl) if negative_pnl != 0 else float('inf')

                # Calculate Sortino ratio
                sortino_ratio = self._calculate_sortino_ratio_optimized(returns_series)

                # Calculate holding periods
                holding_periods = [s.get('holding_period', 1) for s in signals]
                avg_holding_period = np.mean(holding_periods) if holding_periods else 1.0

                return BacktestResults(
                    strategy_id=strategy_id,
                    total_return=total_return,
                    annualized_return=total_return * 252 / max(len(portfolio_values), 1),
                    sharpe_ratio=sharpe_ratio,
                    sortino_ratio=sortino_ratio,
                    max_drawdown=max_drawdown,
                    win_rate=win_rate,
                    profit_factor=profit_factor,
                    total_trades=len(trade_returns_array),
                    avg_trade_return=np.mean(trade_returns_array),
                    best_trade=np.max(trade_returns_array),
                    worst_trade=np.min(trade_returns_array),
                    avg_holding_period=avg_holding_period,
                    greeks_pnl={'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0},
                    volatility_pnl=0.0,
                    time_decay_pnl=0.0
                )
            else:
                return None

        except Exception as e:
            logger.error(f"Fast backtest failed for {strategy.get('strategy_id', 'unknown')}: {e}")
            logger.error(traceback.format_exc())
            return None
    
    def _generate_fast_signals_enhanced(self, prices: np.ndarray, returns: np.ndarray,
                                      volatility: np.ndarray, momentum: np.ndarray,
                                      strategy: Dict) -> List[Dict]:
        """Generate trading signals using enhanced optimized calculations"""
        signals = []

        try:
            strategy_type = strategy.get('strategy_type', 'momentum')
            min_data_points = 30  # Minimum data points needed

            if len(prices) < min_data_points:
                return signals

            # Adjust arrays to same length
            min_len = min(len(prices), len(returns), len(volatility), len(momentum))
            prices = prices[:min_len]
            returns = returns[:min_len]
            volatility = volatility[:min_len]
            momentum = momentum[:min_len]

            # Use NumExpr for complex signal conditions
            if NUMEXPR_AVAILABLE:
                if strategy_type == 'momentum':
                    entry_condition = ne.evaluate(
                        "(momentum > 0.015) & (volatility < 0.25) & (abs(returns) > 0.01)",
                        local_dict={'momentum': momentum, 'volatility': volatility, 'returns': returns}
                    )
                elif strategy_type == 'mean_reversion':
                    entry_condition = ne.evaluate(
                        "(momentum < -0.02) & (volatility > 0.15) & (abs(returns) > 0.015)",
                        local_dict={'momentum': momentum, 'volatility': volatility, 'returns': returns}
                    )
                elif strategy_type == 'volatility_breakout':
                    vol_threshold = np.percentile(volatility, 75)
                    entry_condition = ne.evaluate(
                        "(volatility > vol_threshold) & (abs(momentum) > 0.02)",
                        local_dict={'volatility': volatility, 'momentum': momentum, 'vol_threshold': vol_threshold}
                    )
                else:
                    # Default momentum with enhanced conditions
                    entry_condition = ne.evaluate(
                        "(momentum > 0.01) & (volatility < 0.3) & (returns > 0.005)",
                        local_dict={'momentum': momentum, 'volatility': volatility, 'returns': returns}
                    )
            else:
                # Fallback to numpy
                if strategy_type == 'momentum':
                    entry_condition = (momentum > 0.015) & (volatility < 0.25) & (np.abs(returns) > 0.01)
                else:
                    entry_condition = (momentum < -0.02) & (volatility > 0.15)

            # Find entry points
            entry_indices = np.where(entry_condition)[0]

            # Limit number of signals for performance
            max_signals = min(50, len(entry_indices))
            entry_indices = entry_indices[:max_signals]

            # Generate signals with enhanced exit logic
            for i in entry_indices:
                if i < len(prices) - 10:  # Ensure we have exit data
                    # Dynamic exit based on strategy type and market conditions
                    exit_idx = self._find_optimal_exit_numba(
                        prices, returns, volatility, i, strategy_type
                    )

                    if exit_idx > i and exit_idx < len(prices):
                        # Calculate expected P&L for signal filtering
                        entry_price = prices[i]
                        exit_price = prices[exit_idx]
                        expected_return = (exit_price - entry_price) / entry_price

                        # Only include signals with reasonable expected returns
                        if abs(expected_return) > 0.005:  # 0.5% minimum move
                            signal = {
                                'entry_idx': i,
                                'exit_idx': exit_idx,
                                'entry_price': entry_price,
                                'exit_price': exit_price,
                                'holding_period': exit_idx - i,
                                'signal_type': strategy_type,
                                'expected_return': expected_return,
                                'entry_volatility': volatility[i],
                                'entry_momentum': momentum[i]
                            }
                            signals.append(signal)

            return signals

        except Exception as e:
            logger.error(f"Enhanced signal generation failed: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def _simulate_portfolio_numba_enhanced(self, signals: List[Dict], prices: np.ndarray,
                                          config: BacktestConfig) -> Tuple[np.ndarray, List[float]]:
        """Enhanced portfolio simulation using optimized calculations"""
        try:
            portfolio_values = np.full(len(prices), config.initial_capital, dtype=np.float64)
            trade_pnls = []

            for signal in signals:
                entry_idx = signal['entry_idx']
                exit_idx = signal['exit_idx']
                entry_price = signal['entry_price']
                exit_price = signal['exit_price']

                # Calculate position size based on risk management
                current_capital = portfolio_values[entry_idx]
                risk_amount = current_capital * config.risk_per_trade

                # Position sizing with maximum position limit
                max_position_value = current_capital * 0.1  # Max 10% per position
                position_value = min(risk_amount / 0.02, max_position_value)  # Assuming 2% stop loss
                position_size = position_value / entry_price

                # Calculate P&L with transaction costs
                gross_pnl = position_size * (exit_price - entry_price)
                transaction_costs = config.transaction_cost * 2  # Entry + Exit
                net_pnl = gross_pnl - transaction_costs

                # Apply slippage
                slippage_cost = position_size * entry_price * config.slippage
                final_pnl = net_pnl - slippage_cost

                # Update portfolio values from exit point onwards
                portfolio_values[exit_idx:] += final_pnl
                trade_pnls.append(final_pnl)

            return portfolio_values, trade_pnls

        except Exception as e:
            logger.error(f"Portfolio simulation failed: {e}")
            return np.array([config.initial_capital]), []

    @staticmethod
    @jit(nopython=True) if NUMBA_AVAILABLE else lambda f: f
    def _find_optimal_exit_numba(prices: np.ndarray, returns: np.ndarray,
                                volatility: np.ndarray, entry_idx: int,
                                strategy_type: str) -> int:
        """Find optimal exit point using Numba optimization"""
        try:
            max_holding_period = min(20, len(prices) - entry_idx - 1)
            entry_price = prices[entry_idx]

            # Default exit after max holding period
            default_exit = entry_idx + max_holding_period

            # Look for optimal exit based on strategy type
            for i in range(entry_idx + 1, entry_idx + max_holding_period + 1):
                if i >= len(prices):
                    break

                current_price = prices[i]
                price_change = (current_price - entry_price) / entry_price

                # Exit conditions based on strategy type
                if strategy_type == 'momentum':
                    # Take profit at 3% or stop loss at 1.5%
                    if price_change > 0.03 or price_change < -0.015:
                        return i
                elif strategy_type == 'mean_reversion':
                    # Take profit at 2% or stop loss at 2%
                    if abs(price_change) > 0.02:
                        return i
                else:
                    # Default: take profit at 2.5% or stop loss at 1%
                    if price_change > 0.025 or price_change < -0.01:
                        return i

            return default_exit

        except:
            return entry_idx + 5  # Fallback

    def _calculate_sortino_ratio_optimized(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio using optimized calculations"""
        try:
            if len(returns) < 2:
                return 0.0

            # Use NumExpr for downside deviation calculation
            risk_free_daily = self.risk_free_rate / 252

            if NUMEXPR_AVAILABLE:
                excess_returns = ne.evaluate("returns - risk_free_daily",
                                           local_dict={'returns': returns, 'risk_free_daily': risk_free_daily})
                downside_mask = ne.evaluate("excess_returns < 0",
                                          local_dict={'excess_returns': excess_returns})
                downside_returns = excess_returns[downside_mask]
            else:
                excess_returns = returns - risk_free_daily
                downside_returns = excess_returns[excess_returns < 0]

            if len(downside_returns) == 0:
                return 0.0

            downside_deviation = np.sqrt(np.mean(downside_returns**2))

            if downside_deviation > 0:
                return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Sortino ratio calculation failed: {e}")
            return 0.0


    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio using optimized calculations"""
        try:
            excess_returns = returns - self.risk_free_rate / 252
            downside_returns = excess_returns[excess_returns < 0]
            
            if len(downside_returns) == 0:
                return 0.0
            
            downside_deviation = np.sqrt(np.mean(downside_returns**2))
            return np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Sortino ratio calculation failed: {e}")
            return 0.0

class OptionsBacktestingAgent:
    """
    Options Backtesting Agent for comprehensive strategy testing
    
    Handles:
    - Options pricing model backtesting
    - Greeks evolution simulation
    - Multi-leg strategy P&L calculation
    - Risk-adjusted performance metrics
    - Transaction cost modeling
    """
    
    def __init__(self, config_path: str = "config/options_backtesting_config.yaml"):
        """Initialize Options Backtesting Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"
        self.historical_path = self.data_path / "historical"  # Fixed path
        self.backtest_path = self.data_path / "backtest"
        self.features_path = self.data_path / "features"
        self.results_path = self.data_path / "results"  # Add missing results_path
        
        # Create directories
        self.backtest_path.mkdir(parents=True, exist_ok=True)
        self.results_path.mkdir(parents=True, exist_ok=True)  # Create results directory
        
        # Backtesting state
        self.backtest_results = {}
        self.portfolio_history = []
        
        # Risk-free rate
        self.risk_free_rate = 0.06
        
        # Initialize high-performance backtesting engine
        self.hp_engine = HighPerformanceBacktestEngine(self.risk_free_rate)
        
        # Enhanced Performance monitoring
        self.performance_stats = {
            'total_backtests': 0,
            'avg_backtest_time': 0.0,
            'vectorbt_enabled': VBT_AVAILABLE,
            'numba_enabled': NUMBA_AVAILABLE,
            'numexpr_enabled': NUMEXPR_AVAILABLE,
            'cpu_cores': mp.cpu_count(),
            'optimization_level': self._get_optimization_level(),
            'memory_efficient': True,
            'parallel_processing': True,
            'jit_compilation': NUMBA_AVAILABLE,
            'vectorized_calculations': NUMEXPR_AVAILABLE
        }
        
        logger.info(f"[INIT] Ultra High-Performance Options Backtesting Agent initialized:")
        logger.info(f"[INIT] - VectorBT Ultra-Fast Backtesting: {'YES' if VBT_AVAILABLE else 'NO'}")
        logger.info(f"[INIT] - Numba JIT Compilation: {'YES' if NUMBA_AVAILABLE else 'NO'}")
        logger.info(f"[INIT] - NumExpr Vectorized Math: {'YES' if NUMEXPR_AVAILABLE else 'NO'}")
        logger.info(f"[INIT] - CPU Cores Available: {mp.cpu_count()}")
        logger.info(f"[INIT] - Optimization Level: {self.performance_stats['optimization_level']}")
        logger.info(f"[INIT] - Expected Performance Boost: {self._estimate_performance_boost()}x")

    def _get_optimization_level(self) -> str:
        """Determine the optimization level based on available libraries"""
        if VBT_AVAILABLE and NUMBA_AVAILABLE and NUMEXPR_AVAILABLE:
            return "ULTRA_HIGH"
        elif (VBT_AVAILABLE and NUMBA_AVAILABLE) or (VBT_AVAILABLE and NUMEXPR_AVAILABLE):
            return "HIGH"
        elif VBT_AVAILABLE or NUMBA_AVAILABLE or NUMEXPR_AVAILABLE:
            return "MEDIUM"
        else:
            return "BASIC"

    def _estimate_performance_boost(self) -> float:
        """Estimate performance boost based on optimizations"""
        boost = 1.0

        if VBT_AVAILABLE:
            boost *= 50.0  # VectorBT can be 50x faster for portfolio backtesting
        if NUMBA_AVAILABLE:
            boost *= 10.0  # Numba JIT can be 10x faster for numerical computations
        if NUMEXPR_AVAILABLE:
            boost *= 3.0   # NumExpr can be 3x faster for vectorized expressions

        # Account for parallel processing
        boost *= min(mp.cpu_count(), 4)  # Diminishing returns after 4 cores

        return min(boost, 1000.0)  # Cap at 1000x for realistic estimates
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            logger.info("[SUCCESS] Options Backtesting Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from file"""
        try:
            # Default configuration - Updated for current data
            self.config = BacktestConfig(
                start_date="2025-08-01",  # Updated to match available data (August 2025)
                end_date="2025-08-31",    # Extended to cover all August data
                initial_capital=100000.0,
                risk_per_trade=0.02,
                transaction_cost=20.0,
                slippage=0.01
            )
            logger.info("[CONFIG] Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    async def start(self, **kwargs) -> bool:
        """Enhanced start method with ultra-high-performance backtesting"""
        import time
        start_time = time.time()

        try:
            logger.info("[START] Starting Ultra High-Performance Options Backtesting Agent...")
            logger.info(f"[PERFORMANCE] Optimization Level: {self.performance_stats['optimization_level']}")
            logger.info(f"[PERFORMANCE] Expected Speed Boost: {self._estimate_performance_boost():.1f}x")

            self.is_running = True
            
            # Extract date parameters if provided (for training pipeline)
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')
            
            # Check if strategies file is provided (from YAML loading)
            strategies_file = kwargs.get('strategies_file')

            if strategies_file:
                logger.info("[MODE] YAML strategies mode - using strategies from YAML configuration")
                # Load strategies from YAML file
                strategies = await self._load_strategies_from_file(strategies_file)

                if not strategies:
                    logger.warning("[WARNING] No strategies found in YAML file")
                    return False

                # Load feature engineering data instead of historical data
                features_data = await self._load_features_data()

                if features_data is None:
                    logger.warning("[WARNING] No feature engineering data found")
                    return False

                # Run backtests with YAML strategies and feature data
                await self._run_backtests(strategies, features_data, features_data, None)

                # Generate backtest report
                await self._generate_backtest_report()

                # Save results for AI training
                await self._save_results_for_ai_training()

                logger.info("[SUCCESS] YAML-based backtesting completed")
                return True

            elif from_date and to_date:
                logger.info("[MODE] Training pipeline mode - comprehensive backtesting")
                # Run comprehensive backtesting for training
                success = await self._run_comprehensive_backtesting(from_date, to_date)
                return success
            else:
                logger.info("[MODE] Live mode - standard backtesting")

                # Load strategies to backtest
                strategies = await self._load_strategies()

                if not strategies:
                    logger.warning("[WARNING] No strategies found for backtesting")
                    return False

                # For live mode, prioritize features data over historical data
                # Features data contains more comprehensive information
                features_data = await self._load_features_data()

                # Only load historical data if features data is not available
                historical_data = None
                if features_data is None:
                    logger.info("[FALLBACK] Loading historical data as features data not available")
                    historical_data = await self._load_historical_data()

                    if historical_data is None:
                        logger.warning("[WARNING] No data found (neither features nor historical)")
                        return False
                else:
                    logger.info("[OPTIMIZED] Using features data instead of historical data")

                # Load trading signals
                signals_data = await self._load_signals_data()

                # Run backtests with available data
                primary_data = features_data if features_data is not None else historical_data
                await self._run_backtests(strategies, primary_data, features_data, signals_data)

                # Generate backtest report
                await self._generate_backtest_report()

                # Save results for AI training
                await self._save_results_for_ai_training()
                
                logger.info("[SUCCESS] Ultra High-Performance Backtesting completed")

                # Log performance metrics
                end_time = time.time()
                total_time = end_time - start_time
                self.performance_stats['total_backtests'] += len(self.backtest_results)
                self.performance_stats['avg_backtest_time'] = total_time / max(len(self.backtest_results), 1)

                logger.info(f"[PERFORMANCE] Total execution time: {total_time:.2f} seconds")
                logger.info(f"[PERFORMANCE] Average time per strategy: {self.performance_stats['avg_backtest_time']:.3f} seconds")
                logger.info(f"[PERFORMANCE] Strategies per second: {len(self.backtest_results) / total_time:.1f}")

                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start ultra high-performance agent: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _run_comprehensive_backtesting(self, from_date: str, to_date: str) -> bool:
        """Run comprehensive backtesting for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive backtesting...")
            
            # Load all strategies generated for training
            all_strategies = await self._load_comprehensive_strategies()
            
            if not all_strategies:
                logger.warning("[WARNING] No comprehensive strategies found")
                return False
            
            # Load all historical data and features using lazy evaluation
            comprehensive_data = await self._load_comprehensive_data()
            
            if not comprehensive_data:
                logger.warning("[WARNING] No comprehensive data found")
                return False
            
            # Run backtests for each underlying and timeframe
            backtest_results = {}
            
            for underlying in ['NIFTY', 'BANKNIFTY']:
                logger.info(f"[COMPREHENSIVE] Backtesting {underlying} strategies...")
                
                for timeframe in ['1min', '3min', '5min', '15min']:
                    logger.info(f"[COMPREHENSIVE] Backtesting {underlying} {timeframe} strategies...")
                    
                    # Get strategies for this underlying and timeframe
                    strategies = all_strategies.get(f"{underlying}_{timeframe}", [])
                    
                    if not strategies:
                        logger.warning(f"[WARNING] No strategies found for {underlying} {timeframe}")
                        continue
                    
                    # Get data for this underlying and timeframe
                    data = comprehensive_data.get(f"{underlying}_{timeframe}")
                    
                    if data is None:
                        logger.warning(f"[WARNING] No data found for {underlying} {timeframe}")
                        continue
                    
                    # Determine chunk size based on available memory and data size
                    chunk_size = self._calculate_optimal_chunk_size(timeframe)
                    
                    # Run streaming backtests for this combination
                    results = await self._backtest_strategy_set_streaming(
                        strategies, data, underlying, timeframe, chunk_size
                    )
                    
                    if results:
                        backtest_results[f"{underlying}_{timeframe}"] = results
                        logger.info(f"[SUCCESS] Completed {len(results)} backtests for {underlying} {timeframe}")
            
            # Save comprehensive backtest results
            await self._save_comprehensive_backtest_results(backtest_results)
            
            # Generate comprehensive performance report
            await self._generate_comprehensive_report(backtest_results)
            
            logger.info("[SUCCESS] Comprehensive backtesting completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive backtesting failed: {e}")
            return False
    
    def _calculate_optimal_chunk_size(self, timeframe: str) -> int:
        """Calculate optimal chunk size based on timeframe and available memory"""
        # Adjust chunk size based on timeframe (more granular data = smaller chunks)
        chunk_sizes = {
            '1min': 100000,   # Smaller chunks for 1-minute data
            '3min': 75000,   # Medium chunks for 3-minute data  
            '5min': 75000,   # Larger chunks for 5-minute data
            '15min': 75000  # Largest chunks for 15-minute data
        }
        
        return chunk_sizes.get(timeframe, 50000)
    
    async def _load_comprehensive_strategies(self) -> Dict[str, List]:
        """Load all strategies generated for comprehensive backtesting"""
        try:
            logger.info("[LOAD] Loading comprehensive strategies...")

            all_strategies = {}

            # First, check for YAML-generated strategies
            yaml_strategy_files = list(self.strategies_path.glob("strategies_from_yaml_*.parquet"))

            if yaml_strategy_files:
                logger.info("[LOAD] Found YAML strategies, using those instead of generated strategies")
                # Use the latest YAML strategies file
                latest_yaml_file = max(yaml_strategy_files, key=lambda x: x.stat().st_mtime)

                try:
                    strategies_df = pl.read_parquet(latest_yaml_file)
                    yaml_strategies = strategies_df.to_dicts()

                    # Group strategies by underlying and timeframe
                    for strategy in yaml_strategies:
                        underlyings = strategy.get('underlyings', ['NIFTY', 'BANKNIFTY'])
                        timeframes = strategy.get('timeframes', ['1min', '3min', '5min', '15min'])

                        for underlying in underlyings:
                            for timeframe in timeframes:
                                key = f"{underlying}_{timeframe}"
                                if key not in all_strategies:
                                    all_strategies[key] = []

                                # Create strategy copy for this combination
                                strategy_copy = strategy.copy()
                                strategy_copy['underlying'] = underlying
                                strategy_copy['timeframe'] = timeframe
                                all_strategies[key].append(strategy_copy)

                    logger.info(f"[SUCCESS] Loaded YAML strategies for {len(all_strategies)} combinations")
                    return all_strategies

                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load YAML strategies: {e}")
                    # Fall back to old method

            # Fallback: Look for strategy files (both JSON and parquet for backward compatibility)
            strategy_files = list(self.strategies_path.glob("strategies_*_*.json")) + list(self.strategies_path.glob("strategy_*_*.parquet"))

            for file in strategy_files:
                try:
                    # Extract underlying and timeframe from filename
                    # Expected format: strategies_NIFTY_1min_timestamp.json or strategy_NIFTY_1min_timestamp.parquet
                    parts = file.stem.split('_')
                    if len(parts) >= 3:
                        underlying = parts[1]
                        timeframe = parts[2]
                        key = f"{underlying}_{timeframe}"

                        # Load strategy data based on file type
                        if file.suffix == '.json':
                            import json
                            with open(file, 'r') as f:
                                strategies = json.load(f)
                        else:
                            # Fallback to parquet
                            df = pl.read_parquet(file)
                            strategies = df.to_dicts()
                        
                        # Parse legs JSON back to objects
                        for strategy in strategies:
                            if 'legs' in strategy and isinstance(strategy['legs'], str):
                                strategy['legs'] = json.loads(strategy['legs'])
                        
                        all_strategies[key] = strategies
                        logger.info(f"[LOAD] Loaded {len(strategies)} strategies for {key}")
                
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load strategy file {file}: {e}")
                    continue
            
            logger.info(f"[SUCCESS] Loaded strategies for {len(all_strategies)} combinations")
            return all_strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load comprehensive strategies: {e}")
            return {}
    
    async def _load_comprehensive_data(self) -> Dict[str, pl.LazyFrame]:
        """Load all historical data and features for comprehensive backtesting using lazy evaluation"""
        try:
            logger.info("[LOAD] Loading comprehensive data with lazy evaluation...")
            
            comprehensive_data = {}
            
            for underlying in ['NIFTY', 'BANKNIFTY']:
                for timeframe in ['1min', '3min', '5min', '15min']:
                    key = f"{underlying}_{timeframe}"
                    
                    # Load historical options data lazily
                    options_lazy = await self._load_options_data_lazy(underlying, timeframe)
                    
                    # Load feature data lazily
                    feature_lazy = await self._load_feature_data_lazy(underlying, timeframe)
                    
                    # Load index data lazily
                    index_lazy = await self._load_index_data_lazy(underlying, timeframe)
                    
                    # Combine all data lazily
                    if options_lazy is not None:
                        combined_lazy = options_lazy
                        
                        # Join with features if available
                        if feature_lazy is not None:
                            try:
                                # Get schema without forcing evaluation
                                feature_schema = feature_lazy.collect_schema()
                                feature_cols = ['timestamp'] + [col for col in feature_schema.names() if col != 'timestamp']
                                combined_lazy = combined_lazy.join(
                                    feature_lazy.select(feature_cols),
                                    on='timestamp',
                                    how='left'
                                )
                            except Exception as e:
                                logger.warning(f"[WARNING] Failed to join feature data for {key}: {e}")
                        
                        # Join with index data if available
                        if index_lazy is not None:
                            try:
                                # Rename index columns to avoid conflicts
                                index_renamed = index_lazy.rename({
                                    'open': 'index_open',
                                    'high': 'index_high', 
                                    'low': 'index_low',
                                    'close': 'index_close',
                                    'volume': 'index_volume'
                                })
                                
                                combined_lazy = combined_lazy.join(
                                    index_renamed,
                                    on='timestamp',
                                    how='left'
                                )
                            except Exception as e:
                                logger.warning(f"[WARNING] Failed to join index data for {key}: {e}")
                        
                        comprehensive_data[key] = combined_lazy
                        logger.info(f"[LOAD] Prepared lazy frame for {key} (data ready for streaming)")
            
            logger.info(f"[SUCCESS] Prepared lazy frames for {len(comprehensive_data)} combinations")
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load comprehensive data: {e}")
            return {}
    
    async def _load_options_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load options data for specific underlying and timeframe"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for options data files
            patterns = [
                f"{underlying}_*_{timeframe.upper()}_*.parquet",
                f"{underlying}_*_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(data_path.glob(pattern)))
            
            if not files:
                logger.warning(f"[LOAD] No options data found for {underlying} {timeframe}")
                return None
            
            # Load and combine all files
            dataframes = []
            for file in files:
                try:
                    df = pl.read_parquet(file)
                    if 'underlying' in df.columns:
                        df = df.filter(pl.col('underlying') == underlying)
                    dataframes.append(df)
                except Exception as e:
                    logger.warning(f"[LOAD] Failed to load {file}: {e}")
                    continue
            
            if dataframes:
                combined_df = pl.concat(dataframes)
                return combined_df
            else:
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load options data: {e}")
            return None
    
    async def _load_options_data_lazy(self, underlying: str, timeframe: str) -> Optional[pl.LazyFrame]:
        """Load options data lazily for specific underlying and timeframe"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for options data files
            patterns = [
                f"{underlying}_*_{timeframe.upper()}_*.parquet",
                f"{underlying}_*_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(data_path.glob(pattern)))
            
            if not files:
                logger.warning(f"[LOAD] No options data found for {underlying} {timeframe}")
                return None
            
            # Create lazy frames and combine
            lazy_frames = []
            for file in files:
                try:
                    lazy_df = pl.scan_parquet(str(file))
                    # Use collect_schema() to avoid performance warning and eager evaluation
                    schema = lazy_df.collect_schema()
                    if 'underlying' in schema:
                        lazy_df = lazy_df.filter(pl.col('underlying') == underlying)
                    lazy_frames.append(lazy_df)
                except Exception as e:
                    logger.warning(f"[LOAD] Failed to scan {file}: {e}")
                    continue
            
            if lazy_frames:
                combined_lazy = pl.concat(lazy_frames)
                return combined_lazy
            else:
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load options data lazily: {e}")
            return None
    
    async def _load_feature_data_for_backtesting(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load feature data for backtesting"""
        try:
            feature_path = self.features_path / timeframe
            
            # Look for feature files
            patterns = [
                f"feature_{underlying}_{timeframe}_*.parquet",
                f"feature_{underlying}_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(feature_path.glob(pattern)))
            
            if not files:
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            return pl.read_parquet(latest_file)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load feature data: {e}")
            return None
    
    async def _load_feature_data_lazy(self, underlying: str, timeframe: str) -> Optional[pl.LazyFrame]:
        """Load feature data lazily for backtesting"""
        try:
            feature_path = self.features_path / timeframe
            
            # Look for feature files
            patterns = [
                f"feature_{underlying}_{timeframe}_*.parquet",
                f"feature_{underlying}_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(feature_path.glob(pattern)))
            
            if not files:
                return None
            
            # Load the most recent file lazily
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            return pl.scan_parquet(str(latest_file))
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load feature data lazily: {e}")
            return None
    
    async def _load_index_data_for_backtesting(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load index data for backtesting"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for index data files
            patterns = [
                f"Index_{underlying}_{timeframe.upper()}_*.parquet",
                f"Index_{underlying}_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(data_path.glob(pattern)))
            
            if not files:
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            return pl.read_parquet(latest_file)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load index data: {e}")
            return None
    
    async def _load_index_data_lazy(self, underlying: str, timeframe: str) -> Optional[pl.LazyFrame]:
        """Load index data lazily for backtesting"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for index data files
            patterns = [
                f"Index_{underlying}_{timeframe.upper()}_*.parquet",
                f"Index_{underlying}_*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(data_path.glob(pattern)))
            
            if not files:
                return None
            
            # Load the most recent file lazily
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            return pl.scan_parquet(str(latest_file))
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load index data lazily: {e}")
            return None
    
    async def _backtest_strategy_set_streaming(self, strategies: List[Dict], data_lazy: pl.LazyFrame, 
                                             underlying: str, timeframe: str, chunk_size: int = 50000) -> List[BacktestResults]:
        """Backtest a set of strategies using streaming approach for memory efficiency"""
        try:
            logger.info(f"[BACKTEST] Running streaming backtests for {len(strategies)} strategies...")
            
            results = []
            
            # Process each strategy
            for i, strategy in enumerate(strategies):
                logger.info(f"[BACKTEST] Testing strategy: {strategy.get('name', 'unknown')}")
                
                try:
                    # Apply strategy-specific filters and process in streaming mode
                    strategy_result = await self._backtest_single_strategy_streaming(
                        strategy, data_lazy, underlying, timeframe, chunk_size
                    )
                    
                    if strategy_result:
                        results.append(strategy_result)
                        
                except Exception as e:
                    logger.error(f"[ERROR] Failed to backtest strategy {i}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to run streaming backtests: {e}")
            return []
    
    async def _backtest_single_strategy_streaming(self, strategy: Dict, data_lazy: pl.LazyFrame, 
                                                underlying: str, timeframe: str, chunk_size: int) -> Optional[BacktestResults]:
        """Backtest a single strategy using true streaming approach"""
        try:
            # Initialize strategy state
            portfolio_value = self.config.initial_capital
            positions = []
            trades = []
            max_drawdown = 0.0
            peak_value = portfolio_value
            
            logger.info(f"[STREAM] Processing data in chunks of {chunk_size:,}")
            
            # Apply basic filters and select only essential columns to reduce memory usage
            essential_columns = [
                'timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume',
                'strike_price', 'option_type', 'expiry_date', 'underlying'
            ]
            
            # Get schema to check which columns exist
            schema = data_lazy.collect_schema()
            available_columns = [col for col in essential_columns if col in schema]
            
            filtered_lazy = data_lazy.select(available_columns).filter(
                pl.col('underlying') == underlying
            )
            
            # Use a more memory-efficient approach: process data in smaller batches
            processed_rows = 0
            batch_num = 0
            
            # Instead of loading large chunks, use a sliding window approach
            while True:
                try:
                    # Create a very small sample to check if data exists
                    sample_check = filtered_lazy.slice(batch_num * chunk_size, 1)
                    
                    # Try to collect just one row to see if we have data
                    try:
                        test_row = sample_check.collect()
                        if test_row.is_empty():
                            break
                    except:
                        break
                    
                    # Process a smaller chunk to avoid memory issues
                    small_chunk_size = min(chunk_size, 10000)  # Limit to 10k rows max
                    
                    chunk_lazy = filtered_lazy.slice(
                        batch_num * small_chunk_size, small_chunk_size
                    )
                    
                    # Collect with memory optimization
                    chunk_data = chunk_lazy.collect()
                    
                    if chunk_data.is_empty():
                        break
                    
                    batch_num += 1
                    processed_rows += chunk_data.height
                    
                    # Process this chunk
                    chunk_trades, portfolio_value, positions = await self._process_chunk_for_strategy(
                        strategy, chunk_data, portfolio_value, positions
                    )
                    
                    trades.extend(chunk_trades)
                    
                    # Update drawdown
                    if portfolio_value > peak_value:
                        peak_value = portfolio_value
                    current_drawdown = (peak_value - portfolio_value) / peak_value
                    max_drawdown = max(max_drawdown, current_drawdown)
                    
                    # Log progress and clear memory
                    if batch_num % 5 == 0:
                        logger.info(f"[STREAM] Processed {processed_rows:,} rows, Portfolio: ${portfolio_value:,.2f}")
                    
                    # Clear chunk data to free memory
                    del chunk_data
                    
                    # Limit processing to prevent infinite loops
                    if batch_num > 1000:  # Safety limit
                        logger.warning("[STREAM] Reached batch limit, stopping processing")
                        break
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to process chunk {batch_num}: {e}")
                    break
            
            # Calculate final metrics
            if trades:
                return await self._calculate_strategy_metrics(
                    strategy, trades, portfolio_value, max_drawdown, underlying, timeframe
                )
            else:
                logger.warning(f"[WARNING] No trades generated for strategy")
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest strategy with streaming: {e}")
            return None
    
    async def _process_chunk_for_strategy(self, strategy: Dict, chunk_data: pl.DataFrame, 
                                        portfolio_value: float, positions: List) -> Tuple[List, float, List]:
        """Process a data chunk for a specific strategy"""
        try:
            chunk_trades = []
            
            # Convert to pandas for easier row-by-row processing (if needed)
            # Or keep in Polars and use vectorized operations
            
            # Simple example: process each row for signals
            for row in chunk_data.iter_rows(named=True):
                # Apply strategy logic here
                # This is a simplified example - you'd implement your actual strategy logic
                
                # Example: Simple moving average crossover strategy
                if self._should_enter_position(row, strategy, positions):
                    trade = self._create_trade_entry(row, strategy, portfolio_value)
                    if trade:
                        chunk_trades.append(trade)
                        positions.append(trade)
                        portfolio_value -= trade.get('cost', 0)
                
                elif self._should_exit_position(row, strategy, positions):
                    exit_trades = self._create_trade_exits(row, positions)
                    chunk_trades.extend(exit_trades)
                    for exit_trade in exit_trades:
                        portfolio_value += exit_trade.get('proceeds', 0)
                    positions = [p for p in positions if not p.get('closed', False)]
            
            return chunk_trades, portfolio_value, positions
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to process chunk: {e}")
            return [], portfolio_value, positions
    
    def _should_enter_position(self, row: Dict, strategy: Dict, positions: List) -> bool:
        """Determine if we should enter a position based on strategy rules"""
        # Implement your strategy entry logic here
        # This is a placeholder
        return len(positions) < 3  # Simple position limit
    
    def _should_exit_position(self, row: Dict, strategy: Dict, positions: List) -> bool:
        """Determine if we should exit positions based on strategy rules"""
        # Implement your strategy exit logic here
        # This is a placeholder
        return len(positions) > 0 and row.get('close', 0) > 0
    
    def _create_trade_entry(self, row: Dict, strategy: Dict, portfolio_value: float) -> Optional[Dict]:
        """Create a trade entry"""
        # Implement trade creation logic
        return {
            'timestamp': row.get('timestamp'),
            'symbol': row.get('symbol'),
            'action': 'BUY',
            'price': row.get('close', 0),
            'quantity': 1,
            'cost': row.get('close', 0),
            'type': 'entry'
        }
    
    def _create_trade_exits(self, row: Dict, positions: List) -> List[Dict]:
        """Create trade exits for open positions"""
        exits = []
        for position in positions:
            if not position.get('closed', False):
                exit_trade = {
                    'timestamp': row.get('timestamp'),
                    'symbol': position.get('symbol'),
                    'action': 'SELL',
                    'price': row.get('close', 0),
                    'quantity': position.get('quantity', 1),
                    'proceeds': row.get('close', 0) * position.get('quantity', 1),
                    'type': 'exit'
                }
                exits.append(exit_trade)
                position['closed'] = True
        return exits
    
    async def _calculate_strategy_metrics(self, strategy: Dict, trades: List[Dict], 
                                        final_portfolio_value: float, max_drawdown: float,
                                        underlying: str, timeframe: str) -> BacktestResults:
        """Calculate comprehensive strategy performance metrics"""
        try:
            if not trades:
                return self._create_empty_results(strategy.get('id', 'unknown'))
            
            # Calculate basic metrics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.get('pnl', 0) > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Calculate returns
            initial_capital = self.config.initial_capital
            total_return = (final_portfolio_value - initial_capital) / initial_capital
            
            # Create results object
            results = BacktestResults(
                strategy_id=strategy.get('id', 'unknown'),
                total_return=total_return,
                annualized_return=total_return * (252 / max(total_trades, 1)) if total_trades > 0 else 0.0,  # Rough annualization
                sharpe_ratio=0.0,  # Would need more data to calculate properly
                sortino_ratio=0.0,  # Would need more data to calculate properly
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=1.0 if total_trades == 0 else max(total_return / abs(max_drawdown), 1.0) if max_drawdown != 0 else 1.0,
                total_trades=total_trades,
                avg_trade_return=total_return / max(total_trades, 1) if total_trades > 0 else 0.0,
                best_trade=max([t.get('pnl', 0) for t in trades]) if trades else 0.0,
                worst_trade=min([t.get('pnl', 0) for t in trades]) if trades else 0.0,
                avg_holding_period=1.0,  # Simplified - would need actual holding period calculation
                greeks_pnl={'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0},
                volatility_pnl=0.0,
                time_decay_pnl=0.0,
                underlying=underlying,
                timeframe=timeframe,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                final_portfolio_value=final_portfolio_value,
                trades=trades[:100]  # Limit stored trades to save memory
            )
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate strategy metrics: {e}")
            return self._create_empty_results(strategy.get('id', 'unknown'))
    
    async def _backtest_strategy_set(self, strategies: List[Dict], data: pl.DataFrame, 
                                   underlying: str, timeframe: str) -> List[BacktestResults]:
        """Backtest a set of strategies"""
        try:
            logger.info(f"[BACKTEST] Running backtests for {len(strategies)} strategies...")
            
            results = []
            
            for strategy in strategies:
                try:
                    # Run individual strategy backtest
                    result = await self._backtest_single_strategy(strategy, data, underlying, timeframe)
                    
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to backtest strategy {strategy.get('strategy_id', 'unknown')}: {e}")
                    continue
            
            logger.info(f"[SUCCESS] Completed {len(results)} backtests")
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest strategy set: {e}")
            return []
    
    async def _backtest_single_strategy(self, strategy: Dict, data: pl.DataFrame, 
                                      underlying: str, timeframe: str) -> Optional[BacktestResults]:
        """Backtest a single strategy"""
        try:
            strategy_id = strategy.get('strategy_id', 'unknown')
            logger.info(f"[BACKTEST] Testing strategy: {strategy_id}")
            
            # Initialize backtest variables
            initial_capital = 100000.0
            current_capital = initial_capital
            trades = []
            positions = []
            
            # Get strategy parameters
            legs = strategy.get('legs', [])
            max_loss = strategy.get('max_loss', 1000.0)
            target_profit = strategy.get('target_profit')
            stop_loss = strategy.get('stop_loss')
            
            # Simple backtest logic - entry and exit based on conditions
            entry_signals = await self._generate_entry_signals(strategy, data)
            
            for i, signal in enumerate(entry_signals):
                if signal['action'] == 'ENTER':
                    # Enter position
                    entry_price = signal['price']
                    entry_time = signal['timestamp']
                    
                    # Calculate position size based on risk
                    position_size = min(current_capital * 0.02 / max_loss, 1.0)  # 2% risk
                    
                    # Find exit
                    exit_signal = await self._find_exit_signal(
                        strategy, data, i, entry_time, entry_price, target_profit, stop_loss
                    )
                    
                    if exit_signal:
                        # Calculate P&L
                        pnl = (exit_signal['price'] - entry_price) * position_size
                        
                        # Account for transaction costs
                        transaction_cost = 40.0  # Round trip cost
                        pnl -= transaction_cost
                        
                        current_capital += pnl
                        
                        trade = {
                            'entry_time': entry_time,
                            'exit_time': exit_signal['timestamp'],
                            'entry_price': entry_price,
                            'exit_price': exit_signal['price'],
                            'pnl': pnl,
                            'return_pct': pnl / (entry_price * position_size) if entry_price > 0 else 0
                        }
                        trades.append(trade)
            
            # Calculate performance metrics
            if trades:
                total_return = (current_capital - initial_capital) / initial_capital
                
                returns = [trade['return_pct'] for trade in trades]
                win_rate = len([r for r in returns if r > 0]) / len(returns)
                
                avg_return = np.mean(returns) if returns else 0
                std_return = np.std(returns) if len(returns) > 1 else 0
                
                sharpe_ratio = avg_return / std_return if std_return > 0 else 0
                
                # Calculate max drawdown
                cumulative_returns = np.cumsum(returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = cumulative_returns - running_max
                max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0
                
                # Profit factor
                winning_trades = [r for r in returns if r > 0]
                losing_trades = [r for r in returns if r < 0]
                
                profit_factor = (sum(winning_trades) / abs(sum(losing_trades))) if losing_trades else float('inf')
                
                result = BacktestResults(
                    strategy_id=strategy_id,
                    total_return=total_return,
                    annualized_return=total_return * (252 / len(trades)) if trades else 0,  # Rough annualization
                    sharpe_ratio=sharpe_ratio,
                    sortino_ratio=sharpe_ratio,  # Simplified
                    max_drawdown=abs(max_drawdown),
                    win_rate=win_rate,
                    profit_factor=profit_factor,
                    total_trades=len(trades),
                    avg_trade_return=avg_return,
                    best_trade=max(returns) if returns else 0,
                    worst_trade=min(returns) if returns else 0,
                    avg_holding_period=1.0,  # Simplified
                    greeks_pnl={'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0},  # Placeholder
                    volatility_pnl=0.0,  # Placeholder
                    time_decay_pnl=0.0   # Placeholder
                )
                
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest single strategy: {e}")
            return None
    
    async def _generate_entry_signals(self, strategy: Dict, data: pl.DataFrame) -> List[Dict]:
        """Generate entry signals for strategy"""
        try:
            signals = []
            
            # Simple signal generation based on strategy type
            strategy_type = strategy.get('strategy_type', '')
            
            # For demonstration, generate signals every 100 rows
            for i in range(0, data.height, 100):
                row = data.row(i, named=True)
                
                signal = {
                    'timestamp': row['timestamp'],
                    'price': row.get('close', 100.0),
                    'action': 'ENTER'
                }
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate entry signals: {e}")
            return []
    
    async def _find_exit_signal(self, strategy: Dict, data: pl.DataFrame, entry_idx: int, 
                              entry_time, entry_price: float, target_profit: Optional[float], 
                              stop_loss: Optional[float]) -> Optional[Dict]:
        """Find exit signal for a position"""
        try:
            # Look for exit in next 50 rows (simplified)
            for i in range(entry_idx + 1, min(entry_idx + 51, data.height)):
                row = data.row(i, named=True)
                current_price = row.get('close', entry_price)
                
                # Check stop loss
                if stop_loss and current_price <= entry_price - stop_loss:
                    return {
                        'timestamp': row['timestamp'],
                        'price': current_price,
                        'reason': 'stop_loss'
                    }
                
                # Check target profit
                if target_profit and current_price >= entry_price + target_profit:
                    return {
                        'timestamp': row['timestamp'],
                        'price': current_price,
                        'reason': 'target_profit'
                    }
            
            # Default exit after 50 periods
            if entry_idx + 50 < data.height:
                row = data.row(entry_idx + 50, named=True)
                return {
                    'timestamp': row['timestamp'],
                    'price': row.get('close', entry_price),
                    'reason': 'time_exit'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to find exit signal: {e}")
            return None
    
    async def _save_comprehensive_backtest_results(self, results: Dict[str, List[BacktestResults]]):
        """Save comprehensive backtest results"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Convert results to DataFrame format
            all_results = []
            for key, result_list in results.items():
                for result in result_list:
                    result_dict = asdict(result)
                    result_dict['underlying_timeframe'] = key
                    all_results.append(result_dict)
            
            if all_results:
                df = pl.DataFrame(all_results)
                filename = f"backtest_results_{timestamp}.parquet"
                filepath = self.results_path / filename
                
                df.write_parquet(filepath, compression="brotli")
                logger.info(f"[SAVE] Saved {len(all_results)} backtest results to {filename}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save comprehensive backtest results: {e}")
    
    async def _generate_comprehensive_report(self, results: Dict[str, List[BacktestResults]]):
        """Generate comprehensive performance report"""
        try:
            logger.info("[REPORT] Generating comprehensive performance report...")
            
            # Create summary statistics
            summary = {}
            
            for key, result_list in results.items():
                if result_list:
                    total_returns = [r.total_return for r in result_list]
                    sharpe_ratios = [r.sharpe_ratio for r in result_list]
                    win_rates = [r.win_rate for r in result_list]
                    
                    summary[key] = {
                        'num_strategies': len(result_list),
                        'avg_return': np.mean(total_returns),
                        'best_return': max(total_returns),
                        'worst_return': min(total_returns),
                        'avg_sharpe': np.mean(sharpe_ratios),
                        'avg_win_rate': np.mean(win_rates)
                    }
            
            # Save summary report
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = self.results_path / f"backtest_summary_{timestamp}.json"
            
            async with aiofiles.open(report_file, 'w') as f:
                await f.write(json.dumps(summary, indent=2))
            
            logger.info(f"[SUCCESS] Generated comprehensive report: {report_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate comprehensive report: {e}")
    
    async def _load_strategies(self) -> List[Dict]:
        """Load strategies for backtesting"""
        try:
            logger.info("[LOAD] Loading strategies for backtesting...")
            
            # Find latest strategy file
            strategy_files = list(self.strategies_path.glob("generated_strategies_*.json"))
            
            if not strategy_files:
                logger.warning("[WARNING] No strategy files found")
                return []
            
            latest_file = max(strategy_files, key=lambda x: x.stat().st_mtime)
            
            async with aiofiles.open(latest_file, 'r') as f:
                content = await f.read()
                strategies = json.loads(content)
            
            logger.info(f"[SUCCESS] Loaded {len(strategies)} strategies")
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategies: {e}")
            return []

    async def _load_strategies_from_file(self, strategies_file: str) -> List[Dict]:
        """Load strategies from YAML-generated parquet file"""
        try:
            logger.info(f"[LOAD] Loading strategies from YAML file: {strategies_file}")

            # Load strategies from parquet file
            strategies_df = pl.read_parquet(strategies_file)
            strategies = strategies_df.to_dicts()

            # Convert strategies to the format expected by backtesting
            formatted_strategies = []

            for i, strategy in enumerate(strategies):
                # Create a formatted strategy for backtesting
                formatted_strategy = {
                    'strategy_id': f"yaml_{strategy.get('strategy_name', f'strategy_{i}')}",
                    'strategy_name': strategy.get('strategy_name', f'Strategy {i}'),
                    'strategy_type': strategy.get('strategy_type', 'unknown'),
                    'description': strategy.get('description', ''),
                    'underlying': strategy.get('underlyings', ['NIFTY', 'BANKNIFTY'])[0] if strategy.get('underlyings') else 'NIFTY',
                    'timeframes': strategy.get('timeframes', ['1min', '3min', '5min', '15min']),
                    'parameters': strategy.get('parameters', {}),
                    'entry_conditions': strategy.get('entry_conditions', {}),
                    'exit_conditions': strategy.get('exit_conditions', {}),
                    'risk_management': strategy.get('risk_management', {}),
                    'legs': self._create_strategy_legs(strategy),
                    'max_loss': strategy.get('risk_management', {}).get('max_loss_per_trade', 1000),
                    'target_profit': strategy.get('exit_conditions', {}).get('profit_target', 0.5),
                    'stop_loss': strategy.get('exit_conditions', {}).get('stop_loss', 0.3)
                }
                formatted_strategies.append(formatted_strategy)

            logger.info(f"[SUCCESS] Loaded {len(formatted_strategies)} strategies from YAML")
            return formatted_strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategies from file {strategies_file}: {e}")
            return []

    def _create_strategy_legs(self, strategy: Dict) -> List[Dict]:
        """Create strategy legs based on strategy type"""
        try:
            strategy_type = strategy.get('strategy_type', 'unknown')

            # Simple leg creation based on strategy type
            if strategy_type == 'long_call':
                return [{'action': 'BUY', 'option_type': 'CE', 'quantity': 1}]
            elif strategy_type == 'long_put':
                return [{'action': 'BUY', 'option_type': 'PE', 'quantity': 1}]
            elif strategy_type == 'long_straddle':
                return [
                    {'action': 'BUY', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'BUY', 'option_type': 'PE', 'quantity': 1}
                ]
            elif strategy_type == 'long_strangle':
                return [
                    {'action': 'BUY', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'BUY', 'option_type': 'PE', 'quantity': 1}
                ]
            elif strategy_type == 'bull_call_spread':
                return [
                    {'action': 'BUY', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'SELL', 'option_type': 'CE', 'quantity': 1}
                ]
            elif strategy_type == 'bear_put_spread':
                return [
                    {'action': 'BUY', 'option_type': 'PE', 'quantity': 1},
                    {'action': 'SELL', 'option_type': 'PE', 'quantity': 1}
                ]
            elif strategy_type == 'iron_condor':
                return [
                    {'action': 'SELL', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'BUY', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'SELL', 'option_type': 'PE', 'quantity': 1},
                    {'action': 'BUY', 'option_type': 'PE', 'quantity': 1}
                ]
            elif strategy_type == 'short_straddle':
                return [
                    {'action': 'SELL', 'option_type': 'CE', 'quantity': 1},
                    {'action': 'SELL', 'option_type': 'PE', 'quantity': 1}
                ]
            else:
                # Default to long call
                return [{'action': 'BUY', 'option_type': 'CE', 'quantity': 1}]

        except Exception as e:
            logger.error(f"[ERROR] Failed to create strategy legs: {e}")
            return [{'action': 'BUY', 'option_type': 'CE', 'quantity': 1}]
    
    async def _load_historical_data(self) -> Optional[pl.DataFrame]:
        """Load historical options data with PE/CE filtering"""
        try:
            logger.info("[LOAD] Loading historical data...")

            # Set environment variable to handle timezone parsing issues
            import os
            os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

            # Load data from all timeframes
            all_data = []
            timeframes = ['1min', '3min', '5min', '15min']

            for timeframe in timeframes:
                timeframe_path = self.historical_path / timeframe
                logger.info(f"[DEBUG] Checking path: {timeframe_path}")
                if not timeframe_path.exists():
                    logger.warning(f"[WARNING] Path does not exist: {timeframe_path}")
                    continue

                # Look for historical data files with various patterns
                patterns = [
                    f"historical_{timeframe}_*.parquet",
                    f"historical_{timeframe}.parquet",
                    f"*_{timeframe}_*.parquet",
                    f"*{timeframe}*.parquet"
                ]

                for pattern in patterns:
                    files = list(timeframe_path.glob(pattern))
                    logger.info(f"[DEBUG] Pattern {pattern} found {len(files)} files")
                    if files:
                        # Load the most recent file
                        latest_file = max(files, key=lambda x: x.stat().st_mtime)
                        logger.info(f"[LOAD] Loading {timeframe} data from {latest_file}")

                        # Load data with robust timezone handling using pandas first
                        try:
                            # Load data directly with Polars
                            df = pl.read_parquet(latest_file)

                            # Handle timezone issues directly in Polars with version compatibility
                            if 'datetime' in df.columns:
                                try:
                                    # Check if datetime column has timezone info
                                    dtype_str = str(df['datetime'].dtype)
                                    if 'tz' in dtype_str.lower() or 'timezone' in dtype_str.lower():
                                        # Handle timezone-aware datetime
                                        df = df.with_columns(
                                            pl.col('datetime').dt.replace_time_zone(None).alias('timestamp')
                                        )
                                    else:
                                        df = df.with_columns(pl.col('datetime').alias('timestamp'))
                                except Exception as tz_error:
                                    logger.warning(f"[WARNING] Timezone handling failed for datetime column: {tz_error}")
                                    df = df.with_columns(pl.col('datetime').alias('timestamp'))

                            elif 'timestamp' in df.columns:
                                try:
                                    # Check if timestamp column has timezone info
                                    dtype_str = str(df['timestamp'].dtype)
                                    if 'tz' in dtype_str.lower() or 'timezone' in dtype_str.lower():
                                        # Handle timezone-aware timestamp
                                        df = df.with_columns(
                                            pl.col('timestamp').dt.replace_time_zone(None)
                                        )
                                except Exception as tz_error:
                                    logger.warning(f"[WARNING] Timezone handling failed for timestamp column: {tz_error}")
                                    # Keep timestamp as is

                            # If no timestamp column exists, create one from date and time
                            elif 'date' in df.columns and 'time' in df.columns:
                                try:
                                    df = df.with_columns([
                                        (pl.col('date').cast(pl.Utf8) + pl.lit(' ') + pl.col('time').cast(pl.Utf8))
                                        .str.to_datetime(strict=False)
                                        .alias('timestamp')
                                    ])
                                except Exception as dt_error:
                                    logger.warning(f"[WARNING] Date/time combination failed: {dt_error}")
                                    df = df.with_columns([pl.lit(datetime.now()).alias('timestamp')])
                            else:
                                # If no suitable timestamp column, add a default one
                                df = df.with_columns([pl.lit(datetime.now()).alias('timestamp')])

                        except Exception as e:
                            logger.error(f"[ERROR] Failed to load {latest_file} with Polars: {e}")
                            continue

                        # Ensure consistent schema across timeframes
                        required_columns = ['symbol', 'option_type', 'strike_price', 'open', 'high', 'low', 'close', 'volume', 'timestamp']

                        # Add missing columns if needed
                        for col in required_columns:
                            if col not in df.columns:
                                if col == 'option_type':
                                    # Try to infer option type from symbol
                                    df = df.with_columns([
                                        pl.when(pl.col('symbol').str.contains('CE'))
                                        .then(pl.lit('CE'))
                                        .when(pl.col('symbol').str.contains('PE'))
                                        .then(pl.lit('PE'))
                                        .otherwise(pl.lit(None))
                                        .alias('option_type')
                                    ])
                                elif col == 'strike_price':
                                    # Try to extract strike price from symbol
                                    df = df.with_columns([
                                        pl.col('symbol').str.extract(r'(\d+)(?:CE|PE)', 1).cast(pl.Float64, strict=False).alias('strike_price')
                                    ])
                                elif col == 'timestamp':
                                    # Add current timestamp if missing
                                    df = df.with_columns([pl.lit(datetime.now()).alias('timestamp')])
                                else:
                                    # Add default value for missing columns
                                    df = df.with_columns([pl.lit(0.0).alias(col)])

                        # Add timeframe column
                        df = df.with_columns([
                            pl.lit(timeframe).alias("timeframe")
                        ])

                        # Select only required columns to ensure consistency
                        df = df.select(required_columns + ['timeframe'])

                        all_data.append(df)
                        logger.info(f"[SUCCESS] Loaded {df.height} records from {timeframe}")
                        break

            if not all_data:
                logger.warning("[WARNING] No historical data files found")
                return None

            combined_data = pl.concat(all_data)

            # Filter by date range with timezone handling
            try:
                start_date = datetime.strptime(self.config.start_date, "%Y-%m-%d")
                end_date = datetime.strptime(self.config.end_date, "%Y-%m-%d")

                # Check if timestamp column has timezone info and handle accordingly
                timestamp_dtype = str(combined_data['timestamp'].dtype)
                if 'UTC' in timestamp_dtype or 'tz' in timestamp_dtype.lower():
                    # Convert comparison dates to UTC timezone-aware
                    import pytz
                    start_date = pytz.UTC.localize(start_date)
                    end_date = pytz.UTC.localize(end_date)

                    date_filtered = combined_data.filter(
                        (pl.col("timestamp") >= start_date) &
                        (pl.col("timestamp") <= end_date)
                    )
                else:
                    # Use naive datetime comparison
                    date_filtered = combined_data.filter(
                        (pl.col("timestamp") >= start_date) &
                        (pl.col("timestamp") <= end_date)
                    )
            except Exception as date_error:
                logger.warning(f"[WARNING] Date filtering failed: {date_error}, using all data")
                date_filtered = combined_data

            # Apply PE/CE filtering for backtesting
            # Backtesting should primarily use PE/CE options data
            options_data = date_filtered.filter(
                pl.col('option_type').is_not_null() &
                pl.col('option_type').is_in(['PE', 'CE'])
            )

            # Keep index data for reference (moneyness calculation)
            index_data = date_filtered.filter(
                pl.col('option_type').is_null() &
                (pl.col('symbol').str.contains('NIFTY') | pl.col('symbol').str.contains('BANKNIFTY'))
            )

            # Combine options and index data
            filtered_data = pl.concat([options_data, index_data])

            logger.info(f"[SUCCESS] Loaded {filtered_data.height} filtered records "
                       f"({options_data.height} options, {index_data.height} index)")

            return filtered_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical data: {e}")
            return None

    async def _load_features_data(self) -> Optional[pl.DataFrame]:
        """Load comprehensive feature engineering outputs"""
        try:
            logger.info("[LOAD] Loading feature engineering data...")
            import os
            os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

            all_features = []
            timeframes = ['1min', '3min', '5min', '15min']
            underlyings = ['NIFTY', 'BANKNIFTY']
            feature_types = ['features', 'options', 'technical', 'quant', 'regime']

            for timeframe in timeframes:
                timeframe_path = self.features_path / timeframe
                if not timeframe_path.exists():
                    continue

                for underlying in underlyings:
                    for feature_type in feature_types:
                        # Look for feature files
                        pattern = f"{underlying}_{timeframe}_{feature_type}_*.parquet"
                        files = list(timeframe_path.glob(pattern))

                        if files:
                            # Load the most recent file
                            latest_file = max(files, key=lambda x: x.stat().st_mtime)

                            try:
                                df = pl.read_parquet(latest_file)

                                # Add metadata columns
                                df = df.with_columns([
                                    pl.lit(timeframe).alias("timeframe"),
                                    pl.lit(underlying).alias("underlying"),
                                    pl.lit(feature_type).alias("feature_type")
                                ])

                                all_features.append(df)
                                logger.info(f"[SUCCESS] Loaded {df.height} {feature_type} features for {underlying} {timeframe}")

                            except Exception as e:
                                logger.warning(f"[WARNING] Failed to load {latest_file}: {e}")

            if not all_features:
                logger.warning("[WARNING] No feature files found")
                return None

            # Combine all features
            combined_features = pl.concat(all_features)
            logger.info(f"[SUCCESS] Loaded {combined_features.height} total feature records")

            return combined_features

        except Exception as e:
            logger.error(f"[ERROR] Failed to load features data: {e}")
            return None

    async def _load_signals_data(self) -> Optional[pl.DataFrame]:
        """Load signals from signal generation agent"""
        try:
            logger.info("[LOAD] Loading trading signals...")
            import os
            os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

            signals_path = self.data_path / "signals"
            if not signals_path.exists():
                logger.warning("[WARNING] No signals directory found")
                return None

            all_signals = []
            timeframes = ['1min', '3min', '5min', '15min']
            signal_types = ['volatility', 'directional', 'flow', 'greeks']

            for timeframe in timeframes:
                for signal_type in signal_types:
                    # Look for signal files
                    pattern = f"{signal_type}_{timeframe}_*.parquet"
                    files = list(signals_path.glob(pattern))

                    if files:
                        # Load the most recent file
                        latest_file = max(files, key=lambda x: x.stat().st_mtime)

                        try:
                            df = pl.read_parquet(latest_file)

                            # Add metadata columns
                            df = df.with_columns([
                                pl.lit(timeframe).alias("timeframe"),
                                pl.lit(signal_type).alias("signal_type_category")
                            ])

                            all_signals.append(df)
                            logger.info(f"[SUCCESS] Loaded {df.height} {signal_type} signals for {timeframe}")

                        except Exception as e:
                            logger.warning(f"[WARNING] Failed to load {latest_file}: {e}")

            if not all_signals:
                logger.warning("[WARNING] No signal files found")
                return None

            # Combine all signals with schema alignment
            if all_signals:
                # First, analyze the schema of all DataFrames to determine column types
                column_types = {}
                for df in all_signals:
                    for col in df.columns:
                        if col not in column_types:
                            column_types[col] = df[col].dtype

                # Get all unique columns
                all_columns = sorted(list(column_types.keys()))

                # Align all DataFrames to have the same columns with proper type casting
                aligned_signals = []
                for df in all_signals:
                    # Add missing columns with appropriate default values based on existing column types
                    for col in all_columns:
                        if col not in df.columns:
                            # Use the type from the column_types dictionary
                            col_type = column_types[col]
                            if col_type in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]:
                                # Numeric columns get 0.0 or 0
                                df = df.with_columns(pl.lit(0.0).cast(col_type).alias(col))
                            elif col_type == pl.Boolean:
                                # Boolean columns get False
                                df = df.with_columns(pl.lit(False).alias(col))
                            elif 'Datetime' in str(col_type):
                                # Timestamp columns get current time
                                df = df.with_columns(pl.lit(datetime.now()).alias(col))
                            else:
                                # String columns get empty string
                                df = df.with_columns(pl.lit("").cast(pl.Utf8).alias(col))

                    # Reorder columns to match
                    df = df.select(all_columns)
                    aligned_signals.append(df)

                combined_signals = pl.concat(aligned_signals)
                logger.info(f"[SUCCESS] Loaded {combined_signals.height} total signal records")
                return combined_signals
            else:
                return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load signals data: {e}")
            return None

    async def _run_backtests(self, strategies: List[Dict], primary_data: pl.DataFrame,
                           features_data: Optional[pl.DataFrame] = None, signals_data: Optional[pl.DataFrame] = None):
        """Run backtests for all strategies with features and signals"""
        try:
            logger.info(f"[BACKTEST] Running backtests for {len(strategies)} strategies...")
            logger.info(f"[DATA] Primary: {primary_data.height} records")
            if features_data is not None:
                logger.info(f"[DATA] Features: {features_data.height} records")
            if signals_data is not None:
                logger.info(f"[DATA] Signals: {signals_data.height} records")

            # Determine if primary data is features data or historical data
            if 'option_type' in primary_data.columns:
                # Primary data is historical data - filter for PE/CE options
                logger.info("[DATA] Using historical data as primary source")
                options_data = primary_data.filter(
                    pl.col("option_type").is_in(["PE", "CE"])
                )
                backtest_data = options_data
            else:
                # Primary data is features data - use it directly
                logger.info("[DATA] Using features data as primary source")
                backtest_data = primary_data

            logger.info(f"[FOCUS] Backtesting on {backtest_data.height} records")

            # Use high-performance vectorized backtesting
            logger.info("[PERFORMANCE] Using high-performance vectorized backtesting engine")

            # Convert to numpy arrays for maximum performance
            prices = backtest_data.select('close').to_numpy().flatten()
            volumes = backtest_data.select('volume').to_numpy().flatten() if 'volume' in backtest_data.columns else np.ones_like(prices)
            timestamps = backtest_data.select('timestamp').to_numpy().flatten()

            # Pre-calculate technical indicators for all strategies
            tech_indicators = calculate_technical_indicators_numexpr(prices, volumes)
            returns = tech_indicators.get('returns', calculate_returns_numba(prices))
            volatility = tech_indicators.get('volatility', calculate_rolling_std_numba(returns, 20))
            momentum = tech_indicators.get('momentum', np.zeros_like(prices))

            # Use the high-performance backtesting engine
            hp_results = self.hp_engine.vectorized_backtest_portfolio(
                backtest_data, strategies, self.config
            )

            # Store results
            self.backtest_results.update(hp_results)

            logger.info(f"[SUCCESS] High-performance engine completed {len(hp_results)} backtests")

        except Exception as e:
            logger.error(f"[ERROR] Failed to run backtests: {e}")

    def _backtest_strategy_enhanced(self, strategy: Dict, data_package: Dict) -> BacktestResults:
        """Enhanced backtest with features and signals integration"""
        try:
            # Convert back to Polars
            historical_data = pl.from_pandas(data_package['historical'])
            features_data = pl.from_pandas(data_package['features']) if data_package['features'] is not None else None
            signals_data = pl.from_pandas(data_package['signals']) if data_package['signals'] is not None else None

            strategy_id = strategy['strategy_id']
            underlying = strategy.get('underlying', 'NIFTY')

            # Filter data for this underlying and PE/CE options only
            strategy_data = historical_data.filter(
                (pl.col("symbol").str.contains(underlying)) &
                (pl.col("option_type").is_in(["PE", "CE"]))
            ).sort("timestamp")

            if strategy_data.height == 0:
                logger.warning(f"[WARNING] No PE/CE data found for {underlying}")
                return self._create_empty_results(strategy_id)

            # Initialize portfolio
            portfolio_value = self.config.initial_capital
            trades = []

            # Enhanced signal generation using features and signals
            entry_signals = self._generate_enhanced_entry_signals(
                strategy, strategy_data, features_data, signals_data
            )

            logger.info(f"[SIGNALS] Generated {len(entry_signals)} entry signals for {strategy_id}")

            # Execute trades based on signals
            for signal in entry_signals:
                trade_result = self._execute_enhanced_trade(signal, strategy, strategy_data)

                if trade_result:
                    trades.append(trade_result)
                    portfolio_value += trade_result['pnl']

            # Calculate performance metrics
            if not trades:
                logger.warning(f"[WARNING] No trades executed for {strategy_id}")
                return self._create_empty_results(strategy_id)

            results = self._calculate_performance_metrics(strategy_id, trades, portfolio_value)
            logger.info(f"[PERFORMANCE] {strategy_id}: {len(trades)} trades, {results.total_return:.2%} return")

            return results

        except Exception as e:
            logger.error(f"[ERROR] Enhanced backtest failed for {strategy['strategy_id']}: {e}")
            return self._create_empty_results(strategy['strategy_id'])

    def _generate_enhanced_entry_signals(self, strategy: Dict, historical_data: pl.DataFrame,
                                       features_data: Optional[pl.DataFrame], signals_data: Optional[pl.DataFrame]) -> List[Dict]:
        """Generate enhanced entry signals using features and signals"""
        try:
            signals = []

            # Basic momentum-based signals
            if historical_data.height < 50:
                return signals

            # Calculate basic technical indicators
            data_with_indicators = historical_data.with_columns([
                # Price momentum
                (pl.col('close') / pl.col('close').shift(1) - 1).alias('price_momentum'),
                # Volume momentum
                (pl.col('volume') / pl.col('volume').rolling_mean(window_size=10)).alias('volume_momentum'),
                # Volatility
                pl.col('close').rolling_std(window_size=20).alias('volatility')
            ])

            # Generate signals based on conditions
            for i in range(20, data_with_indicators.height - 1):
                row = data_with_indicators.row(i, named=True)

                # Entry conditions for PE/CE options
                if row['option_type'] in ['PE', 'CE']:
                    # Momentum signal
                    if abs(row['price_momentum']) > 0.02:  # 2% price move
                        signal = {
                            'timestamp': row['timestamp'],
                            'symbol': row['symbol'],
                            'option_type': row['option_type'],
                            'strike_price': row['strike_price'],
                            'signal_type': 'momentum',
                            'direction': 'long' if row['price_momentum'] > 0 else 'short',
                            'strength': abs(row['price_momentum']),
                            'entry_price': row['close'],
                            'volume_confirmation': row['volume_momentum'] > 1.2
                        }
                        signals.append(signal)

                        # Limit signals to prevent over-trading
                        if len(signals) >= 100:
                            break

            return signals

        except Exception as e:
            logger.error(f"[ERROR] Enhanced signal generation failed: {e}")
            return []

    def _execute_enhanced_trade(self, signal: Dict, strategy: Dict, historical_data: pl.DataFrame) -> Optional[Dict]:
        """Execute enhanced trade with proper PE/CE options logic"""
        try:
            # Find the exact data point for this signal
            signal_data = historical_data.filter(
                (pl.col('symbol') == signal['symbol']) &
                (pl.col('timestamp') == signal['timestamp'])
            )

            if signal_data.height == 0:
                return None

            entry_row = signal_data.row(0, named=True)

            # Calculate position size based on risk management
            risk_amount = self.config.initial_capital * self.config.risk_per_trade
            position_size = min(risk_amount / entry_row['close'], 100)  # Max 100 contracts

            # Simulate holding period (simplified)
            holding_period = 5  # 5 periods

            # Find exit point
            exit_data = historical_data.filter(
                (pl.col('symbol') == signal['symbol']) &
                (pl.col('timestamp') > signal['timestamp'])
            ).head(holding_period)

            if exit_data.height == 0:
                return None

            exit_row = exit_data.row(-1, named=True)

            # Calculate P&L
            if signal['direction'] == 'long':
                pnl = (exit_row['close'] - entry_row['close']) * position_size
            else:
                pnl = (entry_row['close'] - exit_row['close']) * position_size

            # Apply transaction costs
            transaction_cost = self.config.transaction_cost * 2  # Entry + Exit
            pnl -= transaction_cost

            trade_result = {
                'entry_time': entry_row['timestamp'],
                'exit_time': exit_row['timestamp'],
                'symbol': signal['symbol'],
                'option_type': entry_row['option_type'],
                'strike_price': entry_row['strike_price'],
                'direction': signal['direction'],
                'entry_price': entry_row['close'],
                'exit_price': exit_row['close'],
                'position_size': position_size,
                'pnl': pnl,
                'holding_period': holding_period,
                'signal_strength': signal['strength']
            }

            return trade_result

        except Exception as e:
            logger.error(f"[ERROR] Enhanced trade execution failed: {e}")
            return None
    
    def _backtest_strategy(self, strategy: Dict, historical_data) -> BacktestResults:
        """Backtest a single strategy"""
        try:
            # Convert back to Polars
            data = pl.from_pandas(historical_data)
            
            # Initialize portfolio
            portfolio_value = self.config.initial_capital
            trades = []
            positions = []
            
            # Strategy parameters
            strategy_id = strategy['strategy_id']
            underlying = strategy['underlying']
            legs = strategy['legs']
            
            # Filter data for this underlying
            strategy_data = data.filter(pl.col("symbol").str.contains(underlying)).sort("timestamp")
            
            if strategy_data.height == 0:
                return self._create_empty_results(strategy_id)
            
            # Simulate strategy execution
            entry_signals = self._generate_entry_signals(strategy, strategy_data)
            
            for signal in entry_signals:
                # Execute trade
                trade_result = self._execute_trade(signal, strategy, strategy_data)
                
                if trade_result:
                    trades.append(trade_result)
                    portfolio_value += trade_result['pnl']
            
            # Calculate performance metrics
            if not trades:
                return self._create_empty_results(strategy_id)
            
            results = self._calculate_performance_metrics(strategy_id, trades, portfolio_value)
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest strategy {strategy['strategy_id']}: {e}")
            return self._create_empty_results(strategy['strategy_id'])
    

    
    def _execute_trade(self, signal: Dict, strategy: Dict, data: pl.DataFrame) -> Optional[Dict]:
        """Execute a trade based on signal"""
        try:
            signal_date = signal['date']
            signal_data = signal['data']
            
            # Find entry prices
            entry_prices = {}
            for leg in strategy['legs']:
                leg_data = signal_data.filter(pl.col("symbol") == leg['symbol'])
                
                if leg_data.height == 0:
                    return None
                
                entry_price = leg_data['close'].mean()  # Use average price
                entry_prices[leg['symbol']] = entry_price
            
            # Calculate position size based on risk
            position_size = self._calculate_position_size(strategy, entry_prices)
            
            # Simulate holding period (7-30 days)
            holding_days = np.random.randint(7, 31)
            exit_date = signal_date + timedelta(days=holding_days)
            
            # Find exit prices
            exit_data = data.filter(pl.col("timestamp").dt.date() == exit_date)
            
            if exit_data.height == 0:
                return None
            
            exit_prices = {}
            for leg in strategy['legs']:
                leg_data = exit_data.filter(pl.col("symbol") == leg['symbol'])
                
                if leg_data.height == 0:
                    exit_prices[leg['symbol']] = entry_prices[leg['symbol']]  # No change
                else:
                    exit_prices[leg['symbol']] = leg_data['close'].mean()
            
            # Calculate P&L
            total_pnl = 0
            for leg in strategy['legs']:
                symbol = leg['symbol']
                quantity = leg['quantity'] * position_size
                
                if quantity > 0:  # Long position
                    pnl = (exit_prices[symbol] - entry_prices[symbol]) * quantity
                else:  # Short position
                    pnl = (entry_prices[symbol] - exit_prices[symbol]) * abs(quantity)
                
                total_pnl += pnl
            
            # Apply transaction costs
            total_cost = len(strategy['legs']) * self.config.transaction_cost
            total_pnl -= total_cost
            
            # Apply slippage
            total_pnl *= (1 - self.config.slippage)
            
            return {
                'entry_date': signal_date,
                'exit_date': exit_date,
                'holding_days': holding_days,
                'pnl': total_pnl,
                'entry_prices': entry_prices,
                'exit_prices': exit_prices,
                'position_size': position_size
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to execute trade: {e}")
            return None
    
    def _calculate_position_size(self, strategy: Dict, entry_prices: Dict) -> int:
        """Calculate position size based on risk management"""
        try:
            # Calculate maximum loss for the strategy
            max_loss = strategy.get('max_loss', 10000)
            
            # Risk per trade
            risk_amount = self.config.initial_capital * self.config.risk_per_trade
            
            # Position size
            if max_loss > 0:
                position_size = int(risk_amount / max_loss)
            else:
                position_size = 1
            
            return max(1, position_size)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate position size: {e}")
            return 1
    
    def _calculate_performance_metrics(self, strategy_id: str, trades: List[Dict], 
                                     final_portfolio_value: float) -> BacktestResults:
        """Calculate comprehensive performance metrics"""
        try:
            if not trades:
                return self._create_empty_results(strategy_id)
            
            # Basic metrics
            total_return = (final_portfolio_value - self.config.initial_capital) / self.config.initial_capital
            
            # Trade statistics
            pnls = [trade['pnl'] for trade in trades]
            winning_trades = [pnl for pnl in pnls if pnl > 0]
            losing_trades = [pnl for pnl in pnls if pnl < 0]
            
            win_rate = len(winning_trades) / len(trades) if trades else 0
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            profit_factor = abs(sum(winning_trades) / sum(losing_trades)) if losing_trades else float('inf')
            
            # Risk metrics
            returns = np.array(pnls) / self.config.initial_capital
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            
            # Downside deviation for Sortino ratio
            downside_returns = returns[returns < 0]
            downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0
            sortino_ratio = np.mean(returns) / downside_std * np.sqrt(252) if downside_std > 0 else 0
            
            # Maximum drawdown
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = running_max - cumulative_returns
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
            
            # Holding period
            holding_periods = [trade['holding_days'] for trade in trades]
            avg_holding_period = np.mean(holding_periods)
            
            # Annualized return
            total_days = (datetime.strptime(self.config.end_date, "%Y-%m-%d") - 
                         datetime.strptime(self.config.start_date, "%Y-%m-%d")).days
            annualized_return = (1 + total_return) ** (365 / total_days) - 1
            
            return BacktestResults(
                strategy_id=strategy_id,
                total_return=total_return,
                annualized_return=annualized_return,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=len(trades),
                avg_trade_return=np.mean(pnls),
                best_trade=max(pnls),
                worst_trade=min(pnls),
                avg_holding_period=avg_holding_period,
                greeks_pnl={'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0},  # Placeholder
                volatility_pnl=0,  # Placeholder
                time_decay_pnl=0   # Placeholder
            )
            
        except Exception as e:
            logger.error(f"Failed to calculate performance metrics: {e}")
            return self._create_empty_results(strategy_id)
    
    async def _generate_backtest_report(self):
        """Generate comprehensive backtest report"""
        try:
            logger.info("[REPORT] Generating backtest report...")
            
            if not self.backtest_results:
                logger.warning("[WARNING] No backtest results to report")
                return
            
            # Convert results to DataFrame
            results_data = []
            for strategy_id, result in self.backtest_results.items():
                results_data.append(asdict(result))
            
            results_df = pl.DataFrame(results_data)
            
            # Save detailed results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = self.backtest_path / f"backtest_results_{timestamp}.parquet"
            results_df.write_parquet(results_file)
            
            # Generate summary statistics
            summary = {
                'total_strategies': len(self.backtest_results),
                'profitable_strategies': len([r for r in self.backtest_results.values() if r.total_return > 0]),
                'avg_return': results_df['total_return'].mean(),
                'avg_sharpe': results_df['sharpe_ratio'].mean(),
                'avg_win_rate': results_df['win_rate'].mean(),
                'best_strategy': results_df.sort('total_return', descending=True).row(0, named=True)['strategy_id'],
                'worst_strategy': results_df.sort('total_return').row(0, named=True)['strategy_id']
            }
            
            # Save summary
            summary_file = self.backtest_path / f"backtest_summary_{timestamp}.json"
            async with aiofiles.open(summary_file, 'w') as f:
                await f.write(json.dumps(summary, indent=2, default=str))
            
            logger.info(f"[SUCCESS] Backtest report generated: {len(self.backtest_results)} strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate backtest report: {e}")

    async def _save_results_for_ai_training(self):
        """Save backtest results in format suitable for AI training"""
        try:
            logger.info("[AI_TRAINING] Saving results for AI training...")

            if not self.backtest_results:
                logger.warning("[WARNING] No backtest results to save for AI training")
                return

            # Prepare comprehensive training data format for AI
            training_data = []
            detailed_trades = []

            for strategy_id, result in self.backtest_results.items():
                # Create comprehensive training record
                training_record = {
                    'strategy_id': strategy_id,
                    'timestamp': datetime.now(),
                    'data_type': 'PE_CE_OPTIONS',  # Focus on options data

                    # Primary target variables for AI training
                    'strategy_return': result.total_return,
                    'annualized_return': result.annualized_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'sortino_ratio': result.sortino_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'profit_factor': result.profit_factor,

                    # Trading metrics
                    'total_trades': result.total_trades,
                    'avg_trade_return': result.avg_trade_return,
                    'best_trade': result.best_trade,
                    'worst_trade': result.worst_trade,
                    'avg_holding_period': result.avg_holding_period,

                    # Greeks-based features (for options-specific AI learning)
                    'delta_pnl': result.greeks_pnl.get('delta', 0),
                    'gamma_pnl': result.greeks_pnl.get('gamma', 0),
                    'theta_pnl': result.greeks_pnl.get('theta', 0),
                    'vega_pnl': result.greeks_pnl.get('vega', 0),

                    # Options-specific features
                    'volatility_pnl': result.volatility_pnl,
                    'time_decay_pnl': result.time_decay_pnl,

                    # Risk metrics
                    'risk_adjusted_return': result.total_return / max(result.max_drawdown, 0.01),
                    'profit_per_trade': result.total_return / max(result.total_trades, 1),

                    # Classification labels for AI
                    'performance_class': self._classify_performance(result),
                    'risk_class': self._classify_risk(result),
                    'trade_frequency_class': self._classify_trade_frequency(result)
                }

                training_data.append(training_record)

                # Add individual trade data for detailed AI training
                if hasattr(result, 'trades') and result.trades:
                    for trade in result.trades:
                        trade_record = {
                            'strategy_id': strategy_id,
                            'trade_timestamp': trade.get('entry_time'),
                            'option_type': trade.get('option_type'),
                            'strike_price': trade.get('strike_price'),
                            'direction': trade.get('direction'),
                            'pnl': trade.get('pnl', 0),
                            'holding_period': trade.get('holding_period', 0),
                            'signal_strength': trade.get('signal_strength', 0)
                        }
                        detailed_trades.append(trade_record)

            # Convert to DataFrames
            training_df = pl.DataFrame(training_data)

            # Save comprehensive AI training data
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            ai_training_path = self.data_path / "ai_training"
            ai_training_path.mkdir(parents=True, exist_ok=True)

            # Save strategy-level training data
            strategy_file = ai_training_path / f"strategy_performance_{timestamp}.parquet"
            training_df.write_parquet(strategy_file)

            # Save detailed trade data if available
            if detailed_trades:
                trades_df = pl.DataFrame(detailed_trades)
                trades_file = ai_training_path / f"detailed_trades_{timestamp}.parquet"
                trades_df.write_parquet(trades_file)
                logger.info(f"[AI_TRAINING] Saved {trades_df.height} detailed trade records")

            # Save summary statistics for AI training
            summary_stats = {
                'total_strategies': len(self.backtest_results),
                'avg_return': training_df['strategy_return'].mean(),
                'avg_sharpe': training_df['sharpe_ratio'].mean(),
                'avg_max_drawdown': training_df['max_drawdown'].mean(),
                'pe_ce_focus': True,  # Indicates this is PE/CE options data
                'data_quality': 'high',
                'timeframes_included': ['1min', '3min', '5min', '15min'],
                'features_included': True,
                'signals_included': True
            }

            summary_file = ai_training_path / f"training_summary_{timestamp}.json"
            with open(summary_file, 'w') as f:
                import json
                json.dump(summary_stats, f, indent=2, default=str)

            logger.info(f"[AI_TRAINING] Saved {training_df.height} strategy records for AI training")
            logger.info(f"[AI_TRAINING] Files saved to {ai_training_path}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save AI training data: {e}")

    def _classify_performance(self, result: BacktestResults) -> str:
        """Classify strategy performance for AI training"""
        if result.total_return > 0.2:  # >20% return
            return 'excellent'
        elif result.total_return > 0.1:  # >10% return
            return 'good'
        elif result.total_return > 0:  # Positive return
            return 'moderate'
        else:
            return 'poor'

    def _classify_risk(self, result: BacktestResults) -> str:
        """Classify strategy risk for AI training"""
        if result.max_drawdown < 0.05:  # <5% drawdown
            return 'low'
        elif result.max_drawdown < 0.15:  # <15% drawdown
            return 'medium'
        else:
            return 'high'

    def _classify_trade_frequency(self, result: BacktestResults) -> str:
        """Classify trade frequency for AI training"""
        if result.total_trades > 100:
            return 'high_frequency'
        elif result.total_trades > 20:
            return 'medium_frequency'
        else:
            return 'low_frequency'

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Backtesting Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Backtesting Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Backtesting Agent"""
    agent = OptionsBacktestingAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())