#!/usr/bin/env python3
"""
[GENETIC] Options Strategy Evolution Agent - Comprehensive Adaptive Strategy Optimization

[SUCCESS] CORE FEATURES IMPLEMENTED:
1. [MONITOR] Underperforming Strategy Detection - Auto-identifies evolution candidates
2. [EVOLVE] Strategy Cloning & Mutation Engine - Genetic algorithm-based parameter evolution
3. [EVALUATE] Strategy Evaluation Pipeline Integration - Automated backtesting integration
4. [PROMOTION] Automated Promotion/Demotion Decisions - Performance-based strategy management
5. [REGIME] Market-Regime Adaptation - Regime-aware strategy optimization
6. [ENSEMBLE] Meta-Strategy Fusion (Ensemble) - Combines high-performing strategies
7. [AI] LLM-Assisted Evolution Guidance - Natural language strategy evolution
8. [METRICS] Performance Tagging and Metrics Logging - Comprehensive evolution tracking
9. [EXPERIMENT] Continuous Experimentation Framework - A/B testing for strategies
10. [CYCLE] Self-Learning Feedback Loop - RL-based adaptive parameter tuning
11. [LOGS] Human-Readable Evolution Logs - Natural language evolution summaries
12. [REGISTRY] Strategy Version Registry - Centralized version control system

[BONUS] BONUS FEATURES:
- [EMAIL] Email/Telegram alerts for evolution events
- [GENETIC] Advanced genetic algorithm scoring
- [WINDOWS] Windows environment optimizations
- [DATA] Polars/PyArrow/Polars-TA-Lib integration
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import json
import yaml
import random
import hashlib
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import aiofiles
import aiohttp
from dataclasses import dataclass, asdict
from enum import Enum
import copy
import statistics
import math
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import functools
from collections import defaultdict
import numpy as np

logger = logging.getLogger(__name__)

# Import cupy for GPU acceleration
try:
    import cupy as cp
    CUPY_AVAILABLE = True
    logger.info("[GPU] CuPy available, GPU acceleration enabled.")
except ImportError:
    logging.warning("[GPU] CuPy not available, falling back to CPU (numba).")
    CUPY_AVAILABLE = False
    cp = None

# Import polars-talib for technical indicators
try:
    import polars_talib as ta
except ImportError:
    logging.warning("[WARNING] polars-talib not available, using fallback implementations")
    ta = None

# Import numba for JIT compilation
try:
    from numba import jit, njit
    NUMBA_AVAILABLE = True
except ImportError:
    logging.warning("[WARNING] numba not available, using standard Python implementations")
    NUMBA_AVAILABLE = False
    # Create dummy decorators
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

# Import vectorbt for high-performance backtesting
try:
    import vectorbt as vbt
    VECTORBT_AVAILABLE = True
except ImportError:
    logging.warning("[WARNING] vectorbt not available, using standard implementations")
    VECTORBT_AVAILABLE = False

# [PERFORMANCE] JIT-compiled performance calculation functions
if NUMBA_AVAILABLE:
    @njit
    def calculate_sharpe_ratio_fast(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        """Fast Sharpe ratio calculation using numba"""
        if len(returns) == 0:
            return 0.0
        excess_returns = returns - risk_free_rate
        if np.std(excess_returns) == 0:
            return 0.0
        return np.mean(excess_returns) / np.std(excess_returns)

    @njit
    def calculate_max_drawdown_fast(returns: np.ndarray) -> float:
        """Fast maximum drawdown calculation using numba"""
        if len(returns) == 0:
            return 0.0

        # Manual implementation of cumulative maximum for numba compatibility
        cumulative = np.empty(len(returns))
        cumulative[0] = 1 + returns[0]
        for i in range(1, len(returns)):
            cumulative[i] = cumulative[i-1] * (1 + returns[i])

        # Manual implementation of running maximum
        running_max = np.empty(len(cumulative))
        running_max[0] = cumulative[0]
        for i in range(1, len(cumulative)):
            running_max[i] = max(running_max[i-1], cumulative[i])

        # Calculate drawdown
        min_drawdown = 0.0
        for i in range(len(cumulative)):
            if running_max[i] > 0:
                drawdown = (cumulative[i] - running_max[i]) / running_max[i]
                if drawdown < min_drawdown:
                    min_drawdown = drawdown

        return min_drawdown

    @njit
    def calculate_win_rate_fast(returns: np.ndarray) -> float:
        """Fast win rate calculation using numba"""
        if len(returns) == 0:
            return 0.0
        winning_trades = np.sum(returns > 0)
        return winning_trades / len(returns)

    @njit
    def calculate_expectancy_fast(returns: np.ndarray) -> float:
        """Fast expectancy calculation using numba"""
        if len(returns) == 0:
            return 0.0
        return np.mean(returns)

    @njit
    def calculate_profit_factor_fast(returns: np.ndarray) -> float:
        """Fast profit factor calculation using numba"""
        if len(returns) == 0:
            return 1.0
        gross_profit = np.sum(returns[returns > 0])
        gross_loss = np.abs(np.sum(returns[returns < 0]))
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 1.0
        return gross_profit / gross_loss
else:
    # Fallback implementations without numba
    def calculate_sharpe_ratio_fast(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        if len(returns) == 0:
            return 0.0
        excess_returns = returns - risk_free_rate
        if np.std(excess_returns) == 0:
            return 0.0
        return np.mean(excess_returns) / np.std(excess_returns)

    def calculate_max_drawdown_fast(returns: np.ndarray) -> float:
        if len(returns) == 0:
            return 0.0
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)

    def calculate_win_rate_fast(returns: np.ndarray) -> float:
        if len(returns) == 0:
            return 0.0
        winning_trades = np.sum(returns > 0)
        return winning_trades / len(returns)

    def calculate_expectancy_fast(returns: np.ndarray) -> float:
        if len(returns) == 0:
            return 0.0
        return np.mean(returns)

    def calculate_profit_factor_fast(returns: np.ndarray) -> float:
        if len(returns) == 0:
            return 1.0
        gross_profit = np.sum(returns[returns > 0])
        gross_loss = np.abs(np.sum(returns[returns < 0]))
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 1.0
        return gross_profit / gross_loss

# [GPU] CUDA-accelerated performance calculation functions
if CUPY_AVAILABLE:
    @cp.fuse()
    def calculate_sharpe_ratio_gpu(returns: cp.ndarray, risk_free_rate: float = 0.0) -> float:
        """Fast Sharpe ratio calculation using CuPy on GPU"""
        if returns.size == 0:
            return 0.0
        excess_returns = returns - risk_free_rate
        std_dev = excess_returns.std()
        if std_dev == 0:
            return 0.0
        return float(excess_returns.mean() / std_dev)

    @cp.fuse()
    def calculate_max_drawdown_gpu(returns: cp.ndarray) -> float:
        """Fast maximum drawdown calculation using CuPy on GPU"""
        if returns.size == 0:
            return 0.0
        cumulative = cp.cumprod(1 + returns)
        running_max = cp.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return float(cp.min(drawdown))

    @cp.fuse()
    def calculate_win_rate_gpu(returns: cp.ndarray) -> float:
        """Fast win rate calculation using CuPy on GPU"""
        if returns.size == 0:
            return 0.0
        winning_trades = cp.sum(returns > 0)
        return float(winning_trades / returns.size)

    @cp.fuse()
    def calculate_expectancy_gpu(returns: cp.ndarray) -> float:
        """Fast expectancy calculation using CuPy on GPU"""
        if returns.size == 0:
            return 0.0
        return float(cp.mean(returns))

    @cp.fuse()
    def calculate_profit_factor_gpu(returns: cp.ndarray) -> float:
        """Fast profit factor calculation using CuPy on GPU"""
        if returns.size == 0:
            return 1.0
        gross_profit = cp.sum(returns[returns > 0])
        gross_loss = cp.abs(cp.sum(returns[returns < 0]))
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 1.0
        return float(gross_profit / gross_loss)

else:
    # Fallback if cupy is not available
    calculate_sharpe_ratio_gpu = None
    calculate_max_drawdown_gpu = None
    calculate_win_rate_gpu = None
    calculate_expectancy_gpu = None
    calculate_profit_factor_gpu = None

# [DATA] Data Structures and Enums
class StrategyStatus(Enum):
    """Strategy status enumeration"""
    ACTIVE = "active"
    EXPERIMENTAL = "experimental"
    DEPRECATED = "deprecated"
    DISABLED = "disabled"
    PROMOTED = "promoted"
    DEMOTED = "demoted"

class MarketRegime(Enum):
    """Market regime enumeration"""
    TRENDING_BULL = "trending_bull"
    TRENDING_BEAR = "trending_bear"
    SIDEWAYS_LOW_VOL = "sideways_low_vol"
    SIDEWAYS_HIGH_VOL = "sideways_high_vol"
    VOLATILE_UNCERTAIN = "volatile_uncertain"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

class EvolutionReason(Enum):
    """Reason for strategy evolution"""
    UNDERPERFORMANCE = "underperformance"
    REGIME_CHANGE = "regime_change"
    DRAWDOWN_EXCEEDED = "drawdown_exceeded"
    WIN_RATE_DECLINE = "win_rate_decline"
    SHARPE_DEGRADATION = "sharpe_degradation"
    MANUAL_REQUEST = "manual_request"
    SCHEDULED_OPTIMIZATION = "scheduled_optimization"

@dataclass
class StrategyMetrics:
    """Strategy performance metrics"""
    strategy_id: str
    roi: float
    sharpe_ratio: float
    win_rate: float
    max_drawdown: float
    expectancy: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float
    timestamp: datetime
    regime: MarketRegime

    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['regime'] = self.regime.value
        return result

@dataclass
class StrategyConfig:
    """Strategy configuration structure"""
    strategy_id: str
    name: str
    description: str
    parameters: Dict[str, Any]
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, Any]
    market_outlook: str
    volatility_outlook: str
    timeframe: str
    status: StrategyStatus
    parent_id: Optional[str] = None
    version: str = "v1"
    created_at: Optional[datetime] = None
    tags: List[str] = None
    best_regime: Optional[MarketRegime] = None
    worst_regime: Optional[MarketRegime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.tags is None:
            self.tags = []

    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['status'] = self.status.value
        result['created_at'] = self.created_at.isoformat()
        if self.best_regime:
            result['best_regime'] = self.best_regime.value
        if self.worst_regime:
            result['worst_regime'] = self.worst_regime.value
        return result

@dataclass
class EvolutionEvent:
    """Evolution event tracking"""
    event_id: str
    strategy_id: str
    parent_id: Optional[str]
    reason: EvolutionReason
    changes: Dict[str, Any]
    metrics_before: StrategyMetrics
    metrics_after: Optional[StrategyMetrics]
    timestamp: datetime
    description: str
    success: bool = True

    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['reason'] = self.reason.value
        result['timestamp'] = self.timestamp.isoformat()
        result['metrics_before'] = self.metrics_before.to_dict()
        if self.metrics_after:
            result['metrics_after'] = self.metrics_after.to_dict()
        return result

class OptionsStrategyEvolutionAgent:
    """[GENETIC] Options Strategy Evolution Agent - Comprehensive Adaptive Strategy Optimization"""

    def __init__(self, config_path: str = "config/options_strategy_evolution_config.yaml"):
        """Initialize the Strategy Evolution Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        self.gpu_acceleration = False

        # [DATA] Data paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"
        self.evolution_path = self.data_path / "strategy_evolution"
        self.performance_path = self.data_path / "performance"
        self.backtest_path = self.data_path / "backtest"
        self.registry_path = self.evolution_path / "registry"
        self.experiments_path = self.evolution_path / "experiments"
        self.logs_path = self.evolution_path / "logs"

        # Create directories
        for path in [self.evolution_path, self.registry_path, self.experiments_path, self.logs_path]:
            path.mkdir(parents=True, exist_ok=True)

        # [REGISTRY] Strategy registry and tracking
        self.strategy_registry: Dict[str, StrategyConfig] = {}
        self.performance_history: Dict[str, List[StrategyMetrics]] = defaultdict(list)
        self.evolution_history: List[EvolutionEvent] = []
        self.active_experiments: Dict[str, Dict] = {}
        self.market_regime_cache: Optional[MarketRegime] = None

        # [PERFORMANCE] Performance optimization components - optimized for speed
        self.performance_cache: Dict[str, Any] = {}
        self.computation_cache: Dict[str, Any] = {}

        # Optimize worker pools based on CPU cores with memory consideration
        import os
        cpu_count = os.cpu_count() or 4
        # Use ProcessPoolExecutor for CPU-bound tasks to bypass GIL and leverage multiple cores
        self.process_pool = ProcessPoolExecutor(max_workers=max(1, cpu_count - 1))
        # Use ThreadPoolExecutor for I/O-bound tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=min(cpu_count * 2, 16))

        # [VECTORIZATION] Polars DataFrames for vectorized operations
        self.performance_df: pl.DataFrame = pl.DataFrame()
        self.strategy_metrics_df: Optional[pl.DataFrame] = None

        # [BATCH] Batch processing settings
        self.batch_size = 10
        self.max_concurrent_mutations = 8

        # [GENETIC] Genetic Algorithm parameters
        self.population_size = 50
        self.mutation_rate = 0.15
        self.crossover_rate = 0.8
        self.selection_pressure = 0.3
        self.elite_percentage = 0.1

        # [DATA] Performance thresholds
        self.performance_thresholds = {
            'min_roi': 0.05,  # 5% minimum ROI
            'min_sharpe': 0.5,  # Minimum Sharpe ratio
            'min_win_rate': 0.45,  # 45% minimum win rate
            'max_drawdown': 0.15,  # 15% maximum drawdown
            'min_trades': 10,  # Minimum trades for evaluation
            'min_expectancy': 0.02  # Minimum expectancy
        }

        # [CYCLE] Evolution intervals (in seconds) - Optimized for EFFICIENCY
        self.intervals = {
            'performance_check': 300,    # 5 minutes (reduced I/O)
            'regime_adaptation': 900,    # 15 minutes (reduced overhead)
            'diversity_maintenance': 1800,  # 30 minutes (better batching)
            'full_evolution': 1800,      # 30 minutes (optimized cycles)
            'registry_cleanup': 3600,    # 1 hour (reduced cleanup overhead)
            'production_sync': 7200      # 2 hours (sync evolved strategies to production)
        }

        # [EFFICIENCY] Performance tracking with memory management
        self.last_performance_check = 0
        self.last_evolution_cycle = 0
        self.evolution_cycle_count = 0
        self.performance_check_count = 0
        self.memory_usage_mb = 0
        self.last_memory_cleanup = 0
        self.cache_hit_rate = 0.0
        self.cache_hits = 0
        self.cache_misses = 0

        # [BATCH] Batch processing configuration for performance
        self.batch_size = min(20, cpu_count * 2)  # Smaller batches for memory efficiency
        self.enable_batch_processing = True
        self.chunk_size = 1000  # For data processing chunks
        self.memory_limit_mb = 512  # Memory usage limit

        # [MODE] Performance mode configuration
        self.fast_mode = True  # Set to False for production stability

        # Apply performance mode settings
        self._configure_performance_mode()

        # [EMAIL] Notification settings
        self.notifications = {
            'email_enabled': False,
            'telegram_enabled': False,
            'email_config': {},
            'telegram_config': {}
        }

        logger.info("[GENETIC] [INIT] Options Strategy Evolution Agent initialized with comprehensive features")

        # [WINDOWS] Configure logging for Windows compatibility
        self._configure_windows_logging()

    def _configure_performance_mode(self):
        """Configure performance settings based on mode"""
        if self.fast_mode:
            # Fast mode for development/testing (current settings)
            logger.info("[PERFORMANCE] Fast mode enabled - optimized for speed")
        else:
            # Production mode - conservative intervals
            self.intervals = {
                'performance_check': 600,   # 10 minutes
                'regime_adaptation': 1800,  # 30 minutes
                'diversity_maintenance': 3600,  # 1 hour
                'full_evolution': 7200,     # 2 hours
                'registry_cleanup': 14400,  # 4 hours
                'production_sync': 14400    # 4 hours (less frequent in production)
            }
            self.batch_size = min(10, self.batch_size)  # Smaller batches
            logger.info("[PERFORMANCE] Production mode enabled - optimized for stability")

    def set_fast_mode(self, enabled: bool = True):
        """Enable/disable fast mode for development vs production"""
        self.fast_mode = enabled
        self._configure_performance_mode()
        logger.info(f"[PERFORMANCE] Fast mode {'enabled' if enabled else 'disabled'}")
    
    def _configure_windows_logging(self):
        """[WINDOWS] Configure logging for Windows compatibility"""
        try:
            import sys
            import codecs

            # Set console encoding to UTF-8 for Windows
            if sys.platform.startswith('win'):
                # Try to set UTF-8 encoding for console output
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except (AttributeError, OSError):
                    # Fallback for older Python versions or restricted environments
                    pass

                # Configure logging formatter without emojis for Windows
                for handler in logging.getLogger().handlers:
                    if hasattr(handler, 'setFormatter'):
                        formatter = logging.Formatter(
                            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                        )
                        handler.setFormatter(formatter)

        except Exception as e:
            # Silently handle configuration errors
            pass

    def _safe_log(self, level: str, message: str, *args, **kwargs):
        """[WINDOWS] Safe logging that handles Unicode issues"""
        try:
            # Remove emojis and special Unicode characters for Windows compatibility
            safe_message = message.encode('ascii', 'ignore').decode('ascii')
            getattr(logger, level.lower())(safe_message, *args, **kwargs)
        except Exception:
            # Fallback to basic logging
            getattr(logger, level.lower())(f"[{level.upper()}] Message encoding error", *args, **kwargs)

    async def initialize(self, **kwargs) -> bool:
        """Initialize the Strategy Evolution Agent with optional parameters"""
        try:
            self._safe_log("INFO", "[INIT] Initializing Strategy Evolution Agent...")

            # Store kwargs for later use
            self.init_kwargs = kwargs

            # Load configuration
            await self._load_config()

            # Load existing strategy registry
            await self._load_strategy_registry()

            # Load performance history
            await self._load_performance_history()

            # Load evolution history
            await self._load_evolution_history()

            # Initialize market regime detection
            await self._initialize_regime_detection()

            # Setup notification systems
            await self._setup_notifications()

            logger.info("[SUCCESS] [SUCCESS] Strategy Evolution Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to initialize agent: {e}")
            return False

    async def _load_config(self):
        """📋 Load configuration from YAML file"""
        try:
            if Path(self.config_path).exists():
                async with aiofiles.open(self.config_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    self.config = yaml.safe_load(content)
                self._safe_log("INFO", f"[CONFIG] Loaded configuration from {self.config_path}")
            else:
                # Default configuration
                self.config = {
                    'genetic_algorithm': {
                        'population_size': 50,
                        'generations': 100,
                        'mutation_rate': 0.15,
                        'crossover_rate': 0.8,
                        'selection_pressure': 0.3,
                        'elite_percentage': 0.1
                    },
                    'performance_thresholds': {
                        'min_roi': 0.05,
                        'min_sharpe': 0.5,
                        'min_win_rate': 0.45,
                        'max_drawdown': 0.15,
                        'min_trades': 10,
                        'min_expectancy': 0.02
                    },
                    'evolution_intervals': {
                        'performance_check': 300,
                        'regime_adaptation': 900,
                        'diversity_maintenance': 1800,
                        'full_evolution': 3600,
                        'registry_cleanup': 7200,
                        'production_sync': 7200
                    },
                    'notifications': {
                        'email_enabled': False,
                        'telegram_enabled': False,
                        'email_config': {
                            'smtp_server': 'smtp.gmail.com',
                            'smtp_port': 587,
                            'username': '',
                            'password': '',
                            'recipients': []
                        },
                        'telegram_config': {
                            'bot_token': '',
                            'chat_ids': []
                        }
                    },
                    'mutation_parameters': {
                        'rsi_range': [5, 25],
                        'ma_range': [5, 50],
                        'stop_loss_range': [0.01, 0.05],
                        'take_profit_range': [0.02, 0.10],
                        'iv_rank_range': [10, 90],
                        'timeframe_options': ['1min', '3min', '5min', '15min', '30min']
                    }
                }

                # Save default config
                await self._save_config()
                self._safe_log("INFO", "[CONFIG] Created default configuration file")

            # Update instance variables from config
            self._update_from_config()

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to load configuration: {e}")
            raise

    def _update_from_config(self):
        """[CYCLE] Update instance variables from configuration"""
        ga_config = self.config.get('genetic_algorithm') or {}
        self.population_size = ga_config.get('population_size', 50)
        self.mutation_rate = ga_config.get('mutation_rate', 0.15)
        self.crossover_rate = ga_config.get('crossover_rate', 0.8)
        self.selection_pressure = ga_config.get('selection_pressure', 0.3)
        self.elite_percentage = ga_config.get('elite_percentage', 0.1)

        self.performance_thresholds.update(self.config.get('performance_thresholds') or {})
        self.intervals.update(self.config.get('evolution_intervals') or {})
        self.notifications.update(self.config.get('notifications') or {})
        self.gpu_acceleration = self.config.get('performance', {}).get('gpu_acceleration', False) and CUPY_AVAILABLE
        if self.gpu_acceleration:
            logger.info("[GPU] GPU acceleration is enabled in config and CuPy is available.")
        else:
            logger.info("[GPU] GPU acceleration is disabled or CuPy is not available.")

    async def _save_config(self):
        """Save configuration to file"""
        try:
            async with aiofiles.open(self.config_path, 'w', encoding='utf-8') as f:
                await f.write(yaml.dump(self.config, default_flow_style=False, indent=2))
            self._safe_log("INFO", f"[CONFIG] Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save configuration: {e}")
    
    async def _load_strategy_registry(self):
        """[REGISTRY] Load existing strategy registry"""
        try:
            registry_file = self.registry_path / "strategy_registry.json"
            if registry_file.exists():
                async with aiofiles.open(registry_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    registry_data = json.loads(content)

                # Convert to StrategyConfig objects
                for strategy_id, data in registry_data.items():
                    # Convert datetime strings back to datetime objects
                    if 'created_at' in data:
                        data['created_at'] = datetime.fromisoformat(data['created_at'])

                    # Convert enum strings back to enums
                    if 'status' in data:
                        data['status'] = StrategyStatus(data['status'])
                    if 'best_regime' in data and data['best_regime']:
                        data['best_regime'] = MarketRegime(data['best_regime'])
                    if 'worst_regime' in data and data['worst_regime']:
                        data['worst_regime'] = MarketRegime(data['worst_regime'])

                    self.strategy_registry[strategy_id] = StrategyConfig(**data)

                logger.info(f"[REGISTRY] [REGISTRY] Loaded {len(self.strategy_registry)} strategies from registry")
            else:
                logger.info("[REGISTRY] [REGISTRY] No existing registry found, starting fresh")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to load strategy registry: {e}")

    async def _load_performance_history(self):
        """[DATA] Load performance history from optimized Parquet file"""
        try:
            performance_file = self.evolution_path / "performance_history.parquet"
            if performance_file.exists():
                loop = asyncio.get_event_loop()
                
                # Read Parquet file into a Polars DataFrame for speed and memory efficiency
                self.performance_df = await loop.run_in_executor(
                    self.thread_pool,
                    pl.read_parquet,
                    str(performance_file)
                )
                
                if self.performance_df is not None and not self.performance_df.is_empty():
                    logger.info(f"[DATA] [PERFORMANCE] Loaded performance history for {self.performance_df.n_unique('strategy_id')} strategies")
                    # Populate self.performance_history from self.performance_df
                    for row in self.performance_df.to_dicts():
                        try:
                            # Ensure timestamp is a datetime object
                            timestamp = row['timestamp']
                            if isinstance(timestamp, str):
                                timestamp = datetime.fromisoformat(timestamp)
                            
                            metrics = StrategyMetrics(
                                strategy_id=row['strategy_id'],
                                roi=row.get('roi', 0),
                                sharpe_ratio=row.get('sharpe_ratio', 0),
                                win_rate=row.get('win_rate', 0),
                                max_drawdown=row.get('max_drawdown', 0),
                                expectancy=row.get('expectancy', 0),
                                profit_factor=row.get('profit_factor', 1),
                                total_trades=row.get('total_trades', 0),
                                avg_trade_duration=row.get('avg_trade_duration', 0),
                                volatility=row.get('volatility', 0),
                                calmar_ratio=row.get('calmar_ratio', 0),
                                sortino_ratio=row.get('sortino_ratio', 0),
                                timestamp=timestamp,
                                regime=MarketRegime(row['regime'])
                            )
                            self.performance_history[row['strategy_id']].append(metrics)
                        except Exception as e:
                            logger.warning(f"Could not parse performance history row: {row}. Error: {e}")
            else:
                logger.info("[DATA] [PERFORMANCE] No existing performance history found")
                # Initialize empty DataFrame with correct schema
                self.performance_df = pl.DataFrame({
                    'strategy_id': pl.Utf8,
                    'roi': pl.Float64,
                    'sharpe_ratio': pl.Float64,
                    'win_rate': pl.Float64,
                    'max_drawdown': pl.Float64,
                    'expectancy': pl.Float64,
                    'profit_factor': pl.Float64,
                    'total_trades': pl.Int64,
                    'avg_trade_duration': pl.Float64,
                    'volatility': pl.Float64,
                    'calmar_ratio': pl.Float64,
                    'sortino_ratio': pl.Float64,
                    'timestamp': pl.Datetime,
                    'regime': pl.Utf8
                })

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to load performance history: {e}")

    async def _load_evolution_history(self):
        """[GENETIC] Load evolution history"""
        try:
            evolution_file = self.evolution_path / "evolution_history.json"
            if evolution_file.exists():
                async with aiofiles.open(evolution_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    history_data = json.loads(content)

                # Convert to EvolutionEvent objects
                for event_data in history_data:
                    # Convert datetime and enum strings
                    event_data['timestamp'] = datetime.fromisoformat(event_data['timestamp'])
                    event_data['reason'] = EvolutionReason(event_data['reason'])

                    # Convert metrics
                    metrics_before_data = event_data['metrics_before']
                    metrics_before_data['timestamp'] = datetime.fromisoformat(metrics_before_data['timestamp'])
                    metrics_before_data['regime'] = MarketRegime(metrics_before_data['regime'])
                    event_data['metrics_before'] = StrategyMetrics(**metrics_before_data)

                    if event_data.get('metrics_after'):
                        metrics_after_data = event_data['metrics_after']
                        metrics_after_data['timestamp'] = datetime.fromisoformat(metrics_after_data['timestamp'])
                        metrics_after_data['regime'] = MarketRegime(metrics_after_data['regime'])
                        event_data['metrics_after'] = StrategyMetrics(**metrics_after_data)

                    self.evolution_history.append(EvolutionEvent(**event_data))

                logger.info(f"[GENETIC] [EVOLUTION] Loaded {len(self.evolution_history)} evolution events")
            else:
                logger.info("[GENETIC] [EVOLUTION] No existing evolution history found")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to load evolution history: {e}")

    async def _initialize_regime_detection(self):
        """[REGIME] Initialize market regime detection"""
        try:
            # Load current market regime from market monitoring agent
            market_data_file = self.data_path / "live" / "market_summary.json"
            if market_data_file.exists():
                async with aiofiles.open(market_data_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    market_data = json.loads(content)

                # Determine regime based on market data
                self.market_regime_cache = await self._detect_market_regime(market_data)
                logger.info(f"[REGIME] [REGIME] Current market regime: {self.market_regime_cache.value}")
            else:
                self.market_regime_cache = MarketRegime.SIDEWAYS_LOW_VOL  # Default
                logger.info("[REGIME] [REGIME] Using default market regime")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to initialize regime detection: {e}")
            self.market_regime_cache = MarketRegime.SIDEWAYS_LOW_VOL

    async def _setup_notifications(self):
        """[EMAIL] Setup notification systems"""
        try:
            if self.notifications.get('email_enabled', False):
                email_config = self.notifications.get('email_config') or {}
                if email_config.get('username') and email_config.get('password'):
                    logger.info("[EMAIL] [NOTIFICATIONS] Email notifications enabled")
                else:
                    logger.warning("[WARNING] [NOTIFICATIONS] Email enabled but credentials missing")

            if self.notifications.get('telegram_enabled', False):
                telegram_config = self.notifications.get('telegram_config') or {}
                if telegram_config.get('bot_token'):
                    logger.info("📱 [NOTIFICATIONS] Telegram notifications enabled")
                else:
                    logger.warning("[WARNING] [NOTIFICATIONS] Telegram enabled but bot token missing")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to setup notifications: {e}")

    async def start(self, **kwargs) -> bool:
        """Start the Strategy Evolution Agent"""
        try:
            self._safe_log("INFO", "[START] Starting Options Strategy Evolution Agent...")
            self.is_running = True

            # Load existing strategies from strategy generation agent
            await self._sync_with_strategy_generation()

            # Start all evolution processes concurrently
            await asyncio.gather(
                self._monitor_performance(),           # [MONITOR] Feature 1: Underperforming detection
                self._evolve_strategies(),            # [EVOLVE] Feature 2: Cloning & mutation
                self._evaluate_strategies(),          # [EVALUATE] Feature 3: Evaluation pipeline
                self._manage_promotions_demotions(),  # [PROMOTION] Feature 4: Promotion/demotion
                self._adapt_to_market_regime(),       # [REGIME] Feature 5: Market regime adaptation
                self._create_ensemble_strategies(),   # [ENSEMBLE] Feature 6: Meta-strategy fusion
                self._continuous_experimentation(),   # [EXPERIMENT] Feature 9: Experimentation framework
                self._self_learning_loop(),          # [CYCLE] Feature 10: Self-learning feedback
                self._maintain_registry(),           # [REGISTRY] Feature 12: Version registry
                self._generate_evolution_logs(),     # [LOGS] Feature 11: Human-readable logs
                self._sync_strategies_to_production() # [SYNC] Feature 13: Production synchronization
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to start agent: {e}")
            return False

    async def _sync_with_strategy_generation(self):
        """[CYCLE] Sync with Strategy Generation Agent"""
        try:
            # Load latest generated strategies
            strategy_files = list(self.strategies_path.glob("generated_strategies_*.json"))
            if strategy_files:
                latest_file = max(strategy_files, key=lambda x: x.stat().st_mtime)

                async with aiofiles.open(latest_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    strategies = json.loads(content)

                # Add new strategies to registry
                new_count = 0
                for strategy_data in strategies:
                    strategy_id = strategy_data.get('strategy_id', f"strat_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

                    if strategy_id not in self.strategy_registry:
                        # Convert to StrategyConfig
                        config = StrategyConfig(
                            strategy_id=strategy_id,
                            name=strategy_data.get('name', f"Strategy {strategy_id}"),
                            description=strategy_data.get('description', ''),
                            parameters=strategy_data.get('parameters', {}),
                            entry_conditions=strategy_data.get('entry_conditions', []),
                            exit_conditions=strategy_data.get('exit_conditions', []),
                            risk_management=strategy_data.get('risk_management', {}),
                            market_outlook=strategy_data.get('market_outlook', 'neutral'),
                            volatility_outlook=strategy_data.get('volatility_outlook', 'neutral'),
                            timeframe=strategy_data.get('timeframe', '15min'),
                            status=StrategyStatus.ACTIVE,
                            tags=strategy_data.get('tags', [])
                        )

                        self.strategy_registry[strategy_id] = config
                        new_count += 1

                if new_count > 0:
                    await self._save_strategy_registry()
                    logger.info(f"[CYCLE] [SYNC] Added {new_count} new strategies to registry")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to sync with strategy generation: {e}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [MONITOR] FEATURE 1: UNDERPERFORMING STRATEGY DETECTION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _monitor_performance(self):
        """[MONITOR] Monitor strategy performance and detect underperformers"""
        while self.is_running:
            try:
                current_time = asyncio.get_event_loop().time()

                # Log every 2nd check to provide regular feedback
                self.performance_check_count += 1
                if self.performance_check_count % 2 == 1:
                    # Include memory usage in logs
                    self._update_memory_usage()
                    self._safe_log("INFO", f"[MONITOR] Check #{self.performance_check_count}, Memory: {self.memory_usage_mb}MB, Cache hits: {self.cache_hit_rate:.1%}")
                    
                # Periodic memory cleanup
                if self.performance_check_count % 100 == 0:
                    await self._cleanup_memory()

                # Get latest performance data
                underperformers = await self._detect_underperforming_strategies()

                if underperformers:
                    self._safe_log("INFO", f"[MONITOR] Found {len(underperformers)} underperforming strategies")

                    for strategy_id, issues in underperformers.items():
                        await self._flag_for_evolution(strategy_id, issues)

                        # Send notification (without emoji)
                        await self._send_notification(
                            f"Strategy {strategy_id} flagged for evolution",
                            f"Issues detected: {', '.join(issues)}"
                        )

                self.last_performance_check = current_time
                await asyncio.sleep(self.intervals['performance_check'])

            except Exception as e:
                logger.error(f"[ERROR] Performance monitoring failed: {e}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _detect_underperforming_strategies(self) -> Dict[str, List[str]]:
        """[MONITOR] Detect strategies that need evolution"""
        underperformers = {}

        try:
            # Load latest backtest results
            backtest_files = list(self.backtest_path.glob("backtest_results_*.json"))
            if not backtest_files:
                return underperformers

            latest_backtest = max(backtest_files, key=lambda x: x.stat().st_mtime)

            async with aiofiles.open(latest_backtest, 'r', encoding='utf-8') as f:
                content = await f.read()
                backtest_data = json.loads(content)

            # Analyze each strategy
            for strategy_id, results in backtest_data.items():
                if strategy_id not in self.strategy_registry:
                    continue

                issues = []

                # Check ROI degradation
                roi = results.get('total_return', 0)
                if roi < self.performance_thresholds['min_roi']:
                    issues.append(f"ROI below threshold: {roi:.2%} < {self.performance_thresholds['min_roi']:.2%}")

                # Check Sharpe ratio
                sharpe = results.get('sharpe_ratio', 0)
                if sharpe < self.performance_thresholds['min_sharpe']:
                    issues.append(f"Sharpe ratio below threshold: {sharpe:.2f} < {self.performance_thresholds['min_sharpe']:.2f}")

                # Check win rate
                win_rate = results.get('win_rate', 0)
                if win_rate < self.performance_thresholds['min_win_rate']:
                    issues.append(f"Win rate below threshold: {win_rate:.2%} < {self.performance_thresholds['min_win_rate']:.2%}")

                # Check maximum drawdown
                max_dd = abs(results.get('max_drawdown', 0))
                if max_dd > self.performance_thresholds['max_drawdown']:
                    issues.append(f"Drawdown exceeded: {max_dd:.2%} > {self.performance_thresholds['max_drawdown']:.2%}")

                # Check expectancy
                expectancy = results.get('expectancy', 0)
                if expectancy < self.performance_thresholds['min_expectancy']:
                    issues.append(f"Expectancy below threshold: {expectancy:.4f} < {self.performance_thresholds['min_expectancy']:.4f}")

                # Check trade count
                trade_count = results.get('total_trades', 0)
                if trade_count < self.performance_thresholds['min_trades']:
                    issues.append(f"Insufficient trades: {trade_count} < {self.performance_thresholds['min_trades']}")

                # Check for performance degradation over time
                if strategy_id in self.performance_history:
                    recent_performance = self._analyze_performance_trend(strategy_id)
                    if recent_performance['declining']:
                        issues.append(f"Performance declining: {recent_performance['trend']}")

                if issues:
                    underperformers[strategy_id] = issues

            return underperformers

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to detect underperforming strategies: {e}")
            return {}

    def _analyze_performance_trend(self, strategy_id: str) -> Dict[str, Any]:
        """[METRICS] Analyze performance trend for a strategy using vectorized operations"""
        try:
            # Check cache first with hit tracking
            cache_key = f"trend_{strategy_id}_{datetime.now().strftime('%Y%m%d_%H')}"
            if cache_key in self.performance_cache:
                self.cache_hits += 1
                self._update_cache_hit_rate()
                return self.performance_cache[cache_key]
            
            self.cache_misses += 1

            # Filter DataFrame for the specific strategy
            strategy_df = self.performance_df.filter(pl.col("strategy_id") == strategy_id)

            if len(strategy_df) < 3:
                result = {'declining': False, 'trend': 'insufficient_data'}
                self.performance_cache[cache_key] = result
                return result

            # Get recent metrics (last 30 days)
            recent_cutoff = datetime.now() - timedelta(days=30)
            recent_df = strategy_df.filter(pl.col("timestamp") >= recent_cutoff)

            if len(recent_df) < 2:
                result = {'declining': False, 'trend': 'insufficient_recent_data'}
                self.performance_cache[cache_key] = result
                return result

            # Use vectorized operations for trend analysis on the last 5 measurements
            recent_metrics = recent_df.tail(5)
            
            # Vectorized slope calculations
            declining_indicators = 0

            if len(recent_metrics) >= 3:
                # ROI slope
                roi_slope = np.polyfit(range(len(recent_metrics)), recent_metrics['roi'].to_numpy(), 1)[0]
                if roi_slope < -0.01:
                    declining_indicators += 1

                # Sharpe slope
                sharpe_slope = np.polyfit(range(len(recent_metrics)), recent_metrics['sharpe_ratio'].to_numpy(), 1)[0]
                if sharpe_slope < -0.1:
                    declining_indicators += 1

                # Win rate slope
                wr_slope = np.polyfit(range(len(recent_metrics)), recent_metrics['win_rate'].to_numpy(), 1)[0]
                if wr_slope < -0.05:
                    declining_indicators += 1

            is_declining = declining_indicators >= 2
            trend_description = f"{declining_indicators}/3 metrics declining"

            result = {'declining': is_declining, 'trend': trend_description}
            # Cache with size limit
            if len(self.performance_cache) < 1000:
                self.performance_cache[cache_key] = result
            else:
                # Remove oldest entries
                oldest_keys = list(self.performance_cache.keys())[:100]
                for old_key in oldest_keys:
                    del self.performance_cache[old_key]
                self.performance_cache[cache_key] = result
            
            self._update_cache_hit_rate()
            return result

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to analyze performance trend: {e}")
            return {'declining': False, 'trend': 'analysis_error'}

    async def _flag_for_evolution(self, strategy_id: str, issues: List[str]):
        """🚩 Flag strategy for evolution"""
        try:
            if strategy_id in self.strategy_registry:
                # Update strategy status
                self.strategy_registry[strategy_id].status = StrategyStatus.EXPERIMENTAL

                # Add to evolution queue
                evolution_request = {
                    'strategy_id': strategy_id,
                    'reason': EvolutionReason.UNDERPERFORMANCE,
                    'issues': issues,
                    'timestamp': datetime.now(),
                    'priority': len(issues)  # More issues = higher priority
                }

                # Save evolution request
                evolution_queue_file = self.evolution_path / "evolution_queue.json"
                queue = []

                if evolution_queue_file.exists():
                    async with aiofiles.open(evolution_queue_file, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        queue = json.loads(content)

                # Add new request (avoid duplicates)
                existing_ids = [req['strategy_id'] for req in queue]
                if strategy_id not in existing_ids:
                    queue.append({
                        **evolution_request,
                        'timestamp': evolution_request['timestamp'].isoformat(),
                        'reason': evolution_request['reason'].value
                    })

                async with aiofiles.open(evolution_queue_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(queue, indent=2))

                logger.info(f"🚩 [FLAG] Strategy {strategy_id} flagged for evolution: {', '.join(issues)}")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to flag strategy for evolution: {e}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [EVOLVE] FEATURE 2: STRATEGY CLONING & MUTATION ENGINE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _evolve_strategies(self):
        """[EVOLVE] Evolve trading strategies using genetic algorithms"""
        while self.is_running:
            try:
                logger.info("[EVOLVE] Processing strategy evolution queue...")

                # Process evolution queue
                await self._process_evolution_queue()

                # Periodic full evolution cycle
                await self._run_genetic_algorithm()

                await asyncio.sleep(self.intervals['full_evolution'])

            except Exception as e:
                logger.error(f"[ERROR] Strategy evolution failed: {e}")
                await asyncio.sleep(300)  # Wait before retrying

    async def _process_evolution_queue(self):
        """[EVOLVE] Process strategies in evolution queue with batch optimization"""
        try:
            evolution_queue_file = self.evolution_path / "evolution_queue.json"
            if not evolution_queue_file.exists():
                return

            async with aiofiles.open(evolution_queue_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                queue = json.loads(content)

            if not queue:
                return

            # Sort by priority (more issues = higher priority)
            queue.sort(key=lambda x: x.get('priority', 0), reverse=True)

            # Process strategies in batches for better performance
            batch_size = min(self.max_concurrent_mutations, len(queue))
            processed = []
            mutation_tasks = []

            for request in queue[:batch_size]:
                strategy_id = request['strategy_id']

                if strategy_id in self.strategy_registry:
                    logger.info(f"[EVOLVE] [BATCH] Evolving strategy {strategy_id}")

                    # Create mutations asynchronously
                    task = self._create_strategy_mutations(strategy_id, request['issues'])
                    mutation_tasks.append((request, task))

            # Wait for all mutation tasks to complete
            if mutation_tasks:
                results = await asyncio.gather(
                    *[task for _, task in mutation_tasks],
                    return_exceptions=True
                )

                # Process results and update registry
                registry_updates = {}
                for i, (request, _) in enumerate(mutation_tasks):
                    mutations = results[i]
                    if not isinstance(mutations, Exception) and mutations:
                        # Batch update registry
                        for mutation in mutations:
                            registry_updates[mutation.strategy_id] = mutation

                        processed.append(request)

                        # Log evolution event
                        await self._log_evolution_event(
                            strategy_id=request['strategy_id'],
                            reason=EvolutionReason(request['reason']),
                            changes={'mutations_created': len(mutations)},
                            description=f"Created {len(mutations)} mutations for underperforming strategy"
                        )

                # Batch update strategy registry
                self.strategy_registry.update(registry_updates)

            # Remove processed requests
            remaining_queue = [req for req in queue if req not in processed]

            # Batch write to file
            async with aiofiles.open(evolution_queue_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(remaining_queue, indent=2))

            if processed:
                await self._save_strategy_registry()
                logger.info(f"[EVOLVE] [BATCH] Processed {len(processed)} evolution requests in parallel")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to process evolution queue: {e}")

    async def _run_genetic_algorithm(self):
        """[EVOLVE] Run genetic algorithm for strategy evolution"""
        try:
            current_time = asyncio.get_event_loop().time()

            # Skip if not enough time has passed since last evolution
            if current_time - self.last_evolution_cycle < self.intervals['full_evolution']:
                return

            self.evolution_cycle_count += 1

            # Log every cycle to show progress
            self._safe_log("INFO", f"[EVOLVE] [GA] Cycle #{self.evolution_cycle_count}, Population: {len(self.strategy_registry)}")

            if not self.config:
                logger.error("[ERROR] [GA] Configuration not loaded. Aborting genetic algorithm.")
                return

            # Get all active strategies for genetic algorithm
            active_strategies = [
                s for s in self.strategy_registry.values()
                if s.status in [StrategyStatus.ACTIVE, StrategyStatus.EXPERIMENTAL]
            ]

            if len(active_strategies) < 2:
                if self.evolution_cycle_count % 10 == 1:  # Log only occasionally
                    self._safe_log("INFO", "[EVOLVE] [GA] Not enough strategies for genetic algorithm")
                return

            # Limit population growth - only create offspring if population is not too large
            if len(self.strategy_registry) >= self.population_size * 2:
                if self.evolution_cycle_count % 10 == 1:
                    self._safe_log("INFO", "[EVOLVE] [GA] Population limit reached, skipping offspring creation")
                await self._control_population_size()
                return

            # Selection: Choose top performers for breeding
            top_performers = await self._select_top_performers(active_strategies)

            # Crossover: Create limited number of new strategies
            max_offspring = min(5, len(top_performers))  # Limit to 5 offspring per cycle
            offspring = await self._create_crossover_strategies(top_performers, max_offspring)

            # Mutation: Apply mutations to offspring
            for strategy in offspring:
                if random.random() < self.mutation_rate:
                    await self._apply_random_mutations(strategy, self.config.get('mutation_parameters') or {})

            # Add offspring to registry
            offspring_count = 0
            for strategy in offspring:
                self.strategy_registry[strategy.strategy_id] = strategy
                offspring_count += 1

            # Population control: Remove worst performers if population too large
            await self._control_population_size()

            if offspring_count > 0:
                self._safe_log("INFO", f"[EVOLVE] [GA] Created {offspring_count} offspring strategies")

            self.last_evolution_cycle = current_time

        except Exception as e:
            logger.error(f"[ERROR] Genetic algorithm failed: {e}")

    async def _select_top_performers(self, strategies: List[StrategyConfig]) -> List[StrategyConfig]:
        """[EVOLVE] Select top performing strategies for breeding"""
        try:
            # Sort strategies by performance (placeholder - would use actual performance metrics)
            # For now, randomly select some strategies
            num_to_select = max(2, int(len(strategies) * self.selection_pressure))
            return random.sample(strategies, min(num_to_select, len(strategies)))
        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to select top performers: {e}")
            return []

    async def _create_crossover_strategies(self, parents: List[StrategyConfig], max_offspring: int = None) -> List[StrategyConfig]:
        """[EVOLVE] Create new strategies through crossover"""
        try:
            offspring = []
            if max_offspring is None:
                num_offspring = max(1, int(len(parents) * self.crossover_rate))
            else:
                num_offspring = min(max_offspring, max(1, int(len(parents) * self.crossover_rate)))

            for i in range(num_offspring):
                if len(parents) >= 2:
                    parent1, parent2 = random.sample(parents, 2)
                    child = await self._crossover_strategies(parent1, parent2)
                    if child:
                        offspring.append(child)

            return offspring
        except Exception as e:
            logger.error(f"[ERROR] Failed to create crossover strategies: {e}")
            return []

    async def _crossover_strategies(self, parent1: StrategyConfig, parent2: StrategyConfig) -> Optional[StrategyConfig]:
        """[EVOLVE] Create a child strategy from two parents"""
        try:
            # Use fast copy to avoid deepcopy performance overhead
            child = self._fast_copy_strategy(parent1)

            # Generate new ID
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            crossover_hash = hashlib.md5(f"{parent1.strategy_id}_{parent2.strategy_id}_{timestamp}".encode()).hexdigest()[:8]
            child.strategy_id = f"crossover_{crossover_hash}"
            child.parent_id = f"{parent1.strategy_id}+{parent2.strategy_id}"
            child.version = "v1"
            child.status = StrategyStatus.EXPERIMENTAL
            child.created_at = datetime.now()
            child.description = f"Crossover of {parent1.name} and {parent2.name}"
            child.tags = ['genetic_algorithm', 'crossover']

            # Combine entry conditions (take some from each parent)
            if len(parent2.entry_conditions) > 0:
                # Take half conditions from each parent
                half1 = len(child.entry_conditions) // 2
                child.entry_conditions = child.entry_conditions[:half1] + parent2.entry_conditions[half1:]

            # Combine exit conditions similarly
            if len(parent2.exit_conditions) > 0:
                half2 = len(child.exit_conditions) // 2
                child.exit_conditions = child.exit_conditions[:half2] + parent2.exit_conditions[half2:]

            # Average risk management parameters
            if hasattr(parent2, 'risk_management') and hasattr(child, 'risk_management'):
                child.risk_management = {
                    'max_position_size': (child.risk_management.get('max_position_size', 0.1) +
                                        parent2.risk_management.get('max_position_size', 0.1)) / 2,
                    'stop_loss': (child.risk_management.get('stop_loss', 0.02) +
                                parent2.risk_management.get('stop_loss', 0.02)) / 2,
                    'take_profit': (child.risk_management.get('take_profit', 0.05) +
                                  parent2.risk_management.get('take_profit', 0.05)) / 2
                }

            return child

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to crossover strategies: {e}")
            return None

    async def _control_population_size(self):
        """[EVOLVE] Control population size by removing worst performers"""
        try:
            if len(self.strategy_registry) > self.population_size:
                # Remove excess strategies (placeholder - would use actual performance metrics)
                excess = len(self.strategy_registry) - self.population_size
                strategies_to_remove = random.sample(list(self.strategy_registry.keys()), excess)

                for strategy_id in strategies_to_remove:
                    if self.strategy_registry[strategy_id].status == StrategyStatus.EXPERIMENTAL:
                        del self.strategy_registry[strategy_id]
                        logger.info(f"[EVOLVE] [GA] Removed strategy from population: {strategy_id}")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to control population size: {e}")

    async def _create_strategy_mutations(self, parent_id: str, issues: List[str]) -> List[StrategyConfig]:
        """[EVOLVE] Create mutations of a strategy using parallel processing"""
        try:
            parent_strategy = self.strategy_registry[parent_id]

            # Create multiple mutations with different approaches
            mutation_approaches = [
                'conservative',  # Small parameter changes
                'aggressive',   # Larger parameter changes
                'targeted',     # Address specific issues
                'random'        # Random mutations
            ]

            # Use ProcessPoolExecutor for CPU-bound mutation creation to leverage multiple cores
            loop = asyncio.get_event_loop()
            mutation_tasks = []

            for approach in mutation_approaches:
                task = loop.run_in_executor(
                    self.process_pool,
                    self._mutate_strategy_sync,
                    parent_strategy,
                    approach,
                    issues
                )
                mutation_tasks.append(task)

            # Wait for all mutations to complete
            mutations = await asyncio.gather(*mutation_tasks, return_exceptions=True)

            # Filter out exceptions and None values
            valid_mutations = [
                mutation for mutation in mutations
                if mutation is not None and not isinstance(mutation, Exception)
            ]

            logger.info(f"[EVOLVE] [PARALLEL] Created {len(valid_mutations)} mutations for {parent_id}")
            return valid_mutations

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to create strategy mutations: {e}")
            return []

    def _mutate_strategy_sync(self, parent: StrategyConfig, approach: str, issues: List[str]) -> Optional[StrategyConfig]:
        """[EVOLVE] Synchronous version for parallel execution"""
        try:
            # Create deep copy of parent (optimized)
            mutated_config = self._fast_copy_strategy(parent)

            # Generate new ID
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            mutation_hash = hashlib.md5(f"{parent.strategy_id}_{approach}_{timestamp}".encode()).hexdigest()[:8]
            mutated_config.strategy_id = f"{parent.strategy_id}_{approach}_{mutation_hash}"
            mutated_config.parent_id = parent.strategy_id
            mutated_config.version = f"v{len([s for s in self.strategy_registry.values() if s.parent_id == parent.strategy_id]) + 1}"
            mutated_config.status = StrategyStatus.EXPERIMENTAL
            mutated_config.created_at = datetime.now()
            mutated_config.description = f"Mutation of {parent.name} using {approach} approach"

            # Apply mutations based on approach
            mutation_config = self.config.get('mutation_parameters') or {}

            if approach == 'conservative':
                self._apply_conservative_mutations_sync(mutated_config, mutation_config)
            elif approach == 'aggressive':
                self._apply_aggressive_mutations_sync(mutated_config, mutation_config)
            elif approach == 'targeted':
                self._apply_targeted_mutations_sync(mutated_config, issues, mutation_config)
            elif approach == 'random':
                self._apply_random_mutations_sync(mutated_config, mutation_config)

            return mutated_config

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate strategy: {e}")
            return None

    def _fast_copy_strategy(self, strategy: StrategyConfig) -> StrategyConfig:
        """[PERFORMANCE] Fast strategy copying without deep copy overhead"""
        return StrategyConfig(
            strategy_id=strategy.strategy_id,
            name=strategy.name,
            description=strategy.description,
            parameters=strategy.parameters.copy() if strategy.parameters else {},
            entry_conditions=strategy.entry_conditions.copy() if strategy.entry_conditions else [],
            exit_conditions=strategy.exit_conditions.copy() if strategy.exit_conditions else [],
            risk_management=strategy.risk_management.copy() if strategy.risk_management else {},
            market_outlook=strategy.market_outlook,
            volatility_outlook=strategy.volatility_outlook,
            timeframe=strategy.timeframe,
            status=strategy.status,
            parent_id=strategy.parent_id,
            version=strategy.version,
            created_at=strategy.created_at,
            tags=strategy.tags.copy() if strategy.tags else [],
            best_regime=strategy.best_regime,
            worst_regime=strategy.worst_regime
        )

    async def _mutate_strategy(self, parent: StrategyConfig, approach: str, issues: List[str]) -> Optional[StrategyConfig]:
        """[EVOLVE] Create a single mutation of a strategy"""
        try:
            # Use fast copy to avoid deepcopy performance overhead
            mutated_config = self._fast_copy_strategy(parent)

            # Generate new ID
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            mutation_hash = hashlib.md5(f"{parent.strategy_id}_{approach}_{timestamp}".encode()).hexdigest()[:8]
            mutated_config.strategy_id = f"{parent.strategy_id}_{approach}_{mutation_hash}"
            mutated_config.parent_id = parent.strategy_id
            mutated_config.version = f"v{len([s for s in self.strategy_registry.values() if s.parent_id == parent.strategy_id]) + 1}"
            mutated_config.status = StrategyStatus.EXPERIMENTAL
            mutated_config.created_at = datetime.now()
            mutated_config.description = f"Mutation of {parent.name} using {approach} approach"

            # Apply mutations based on approach
            mutation_config = self.config.get('mutation_parameters') or {}

            if approach == 'conservative':
                await self._apply_conservative_mutations(mutated_config, mutation_config)
            elif approach == 'aggressive':
                await self._apply_aggressive_mutations(mutated_config, mutation_config)
            elif approach == 'targeted':
                await self._apply_targeted_mutations(mutated_config, issues, mutation_config)
            elif approach == 'random':
                await self._apply_random_mutations(mutated_config, mutation_config)

            return mutated_config

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate strategy: {e}")
            return None

    async def _apply_conservative_mutations(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Apply conservative mutations (small changes)"""
        try:
            # Small adjustments to technical indicators
            if 'rsi' in str(config.entry_conditions).lower():
                await self._mutate_rsi_parameters(config, mutation_config, factor=0.1)

            if 'ma' in str(config.entry_conditions).lower() or 'ema' in str(config.entry_conditions).lower():
                await self._mutate_ma_parameters(config, mutation_config, factor=0.1)

            # Small adjustments to risk management
            await self._mutate_risk_parameters(config, factor=0.05)

            config.tags.append('conservative_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply conservative mutations: {e}")

    def _apply_conservative_mutations_sync(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Synchronous conservative mutations for parallel execution"""
        try:
            # Small adjustments to technical indicators
            if 'rsi' in str(config.entry_conditions).lower():
                self._mutate_rsi_parameters_sync(config, mutation_config, factor=0.1)

            if 'ma' in str(config.entry_conditions).lower() or 'ema' in str(config.entry_conditions).lower():
                self._mutate_ma_parameters_sync(config, mutation_config, factor=0.1)

            # Small adjustments to risk management
            self._mutate_risk_parameters_sync(config, factor=0.05)

            config.tags.append('conservative_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply conservative mutations: {e}")

    def _apply_aggressive_mutations_sync(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Synchronous aggressive mutations for parallel execution"""
        try:
            # Larger adjustments to technical indicators
            if 'rsi' in str(config.entry_conditions).lower():
                self._mutate_rsi_parameters_sync(config, mutation_config, factor=0.3)

            if 'ma' in str(config.entry_conditions).lower() or 'ema' in str(config.entry_conditions).lower():
                self._mutate_ma_parameters_sync(config, mutation_config, factor=0.3)

            # Larger adjustments to risk management
            self._mutate_risk_parameters_sync(config, factor=0.2)

            # Potentially change timeframe
            timeframes = mutation_config.get('timeframe_options', ['1min', '3min', '5min', '15min', '30min'])
            if random.random() < 0.3:  # 30% chance to change timeframe
                config.timeframe = random.choice(timeframes)

            config.tags.append('aggressive_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply aggressive mutations: {e}")

    def _apply_targeted_mutations_sync(self, config: StrategyConfig, issues: List[str], mutation_config: Dict):
        """[EVOLVE] Synchronous targeted mutations for parallel execution"""
        try:
            for issue in issues:
                if 'roi' in issue.lower() or 'return' in issue.lower():
                    # Increase profit targets, adjust entry conditions
                    self._mutate_profit_targeting_sync(config, increase=True)

                elif 'sharpe' in issue.lower():
                    # Improve risk-adjusted returns
                    self._mutate_risk_parameters_sync(config, factor=0.15, improve_sharpe=True)

                elif 'win rate' in issue.lower():
                    # Make entry conditions more selective
                    self._mutate_entry_selectivity_sync(config, more_selective=True)

                elif 'drawdown' in issue.lower():
                    # Tighten stop losses
                    self._mutate_risk_parameters_sync(config, factor=0.1, tighten_stops=True)

                elif 'expectancy' in issue.lower():
                    # Balance risk/reward ratio
                    self._mutate_risk_reward_ratio_sync(config)

            config.tags.append('targeted_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply targeted mutations: {e}")

    def _apply_random_mutations_sync(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Synchronous random mutations for parallel execution"""
        try:
            # Random chance for each type of mutation
            if random.random() < 0.5:
                self._mutate_rsi_parameters_sync(config, mutation_config, factor=random.uniform(0.1, 0.4))

            if random.random() < 0.5:
                self._mutate_ma_parameters_sync(config, mutation_config, factor=random.uniform(0.1, 0.4))

            if random.random() < 0.7:
                self._mutate_risk_parameters_sync(config, factor=random.uniform(0.05, 0.25))

            # Random timeframe change
            timeframes = mutation_config.get('timeframe_options', ['1min', '3min', '5min', '15min', '30min'])
            if random.random() < 0.2:
                config.timeframe = random.choice(timeframes)

            config.tags.append('random_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply random mutations: {e}")

    async def _apply_aggressive_mutations(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Apply aggressive mutations (larger changes)"""
        try:
            # Larger adjustments to technical indicators
            if 'rsi' in str(config.entry_conditions).lower():
                await self._mutate_rsi_parameters(config, mutation_config, factor=0.3)

            if 'ma' in str(config.entry_conditions).lower() or 'ema' in str(config.entry_conditions).lower():
                await self._mutate_ma_parameters(config, mutation_config, factor=0.3)

            # Larger adjustments to risk management
            await self._mutate_risk_parameters(config, factor=0.2)

            # Potentially change timeframe
            timeframes = mutation_config.get('timeframe_options', ['1min', '3min', '5min', '15min', '30min'])
            if random.random() < 0.3:  # 30% chance to change timeframe
                config.timeframe = random.choice(timeframes)

            config.tags.append('aggressive_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply aggressive mutations: {e}")

    async def _apply_targeted_mutations(self, config: StrategyConfig, issues: List[str], mutation_config: Dict):
        """[EVOLVE] Apply targeted mutations to address specific issues"""
        try:
            for issue in issues:
                if 'roi' in issue.lower() or 'return' in issue.lower():
                    # Increase profit targets, adjust entry conditions
                    await self._mutate_profit_targeting(config, increase=True)

                elif 'sharpe' in issue.lower():
                    # Improve risk-adjusted returns
                    await self._mutate_risk_parameters(config, factor=0.15, improve_sharpe=True)

                elif 'win rate' in issue.lower():
                    # Make entry conditions more selective
                    await self._mutate_entry_selectivity(config, more_selective=True)

                elif 'drawdown' in issue.lower():
                    # Tighten stop losses
                    await self._mutate_risk_parameters(config, factor=0.1, tighten_stops=True)

                elif 'expectancy' in issue.lower():
                    # Balance risk/reward ratio
                    await self._mutate_risk_reward_ratio(config)

            config.tags.append('targeted_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply targeted mutations: {e}")

    async def _apply_random_mutations(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Apply random mutations for exploration"""
        try:
            # Random chance for each type of mutation
            if random.random() < 0.5:
                await self._mutate_rsi_parameters(config, mutation_config, factor=random.uniform(0.1, 0.4))

            if random.random() < 0.5:
                await self._mutate_ma_parameters(config, mutation_config, factor=random.uniform(0.1, 0.4))

            if random.random() < 0.7:
                await self._mutate_risk_parameters(config, factor=random.uniform(0.05, 0.25))

            if random.random() < 0.2:
                # Add new filter
                await self._add_random_filter(config, mutation_config)

            config.tags.append('random_mutation')

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to apply random mutations: {e}")

    async def _mutate_rsi_parameters(self, config: StrategyConfig, mutation_config: Dict, factor: float):
        """[EVOLVE] Mutate RSI parameters"""
        try:
            rsi_range = mutation_config.get('rsi_range', [5, 25])

            # Update entry conditions
            new_conditions = []
            for condition in config.entry_conditions:
                if 'rsi' in condition.lower():
                    # Extract current RSI value and modify
                    import re
                    rsi_match = re.search(r'rsi[_\(](\d+)', condition.lower())
                    if rsi_match:
                        current_period = int(rsi_match.group(1))
                        new_period = max(rsi_range[0], min(rsi_range[1],
                                       int(current_period * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'rsi[_\(](\d+)', f'rsi_{new_period}', condition, flags=re.IGNORECASE)

                    # Modify threshold values
                    threshold_match = re.search(r'[><=]\s*(\d+)', condition)
                    if threshold_match:
                        current_threshold = int(threshold_match.group(1))
                        new_threshold = max(10, min(90,
                                          int(current_threshold * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'([><=]\s*)(\d+)', f'\\g<1>{new_threshold}', condition)

                new_conditions.append(condition)

            config.entry_conditions = new_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate RSI parameters: {e}")

    async def _mutate_ma_parameters(self, config: StrategyConfig, mutation_config: Dict, factor: float):
        """[EVOLVE] Mutate Moving Average parameters"""
        try:
            ma_range = mutation_config.get('ma_range', [5, 50])

            # Update entry conditions
            new_conditions = []
            for condition in config.entry_conditions:
                if any(ma_type in condition.lower() for ma_type in ['ma', 'ema', 'sma']):
                    # Extract and modify MA periods
                    import re
                    ma_match = re.search(r'(ma|ema|sma)[_\(](\d+)', condition.lower())
                    if ma_match:
                        ma_type = ma_match.group(1)
                        current_period = int(ma_match.group(2))
                        new_period = max(ma_range[0], min(ma_range[1],
                                       int(current_period * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'(ma|ema|sma)[_\(](\d+)', f'{ma_type}_{new_period}',
                                         condition, flags=re.IGNORECASE)

                new_conditions.append(condition)

            config.entry_conditions = new_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate MA parameters: {e}")

    async def _mutate_risk_parameters(self, config: StrategyConfig, factor: float,
                                    improve_sharpe: bool = False, tighten_stops: bool = False):
        """[EVOLVE] Mutate risk management parameters"""
        try:
            risk_mgmt = config.risk_management.copy()

            # Mutate stop loss
            if 'stop_loss' in risk_mgmt:
                current_sl = float(risk_mgmt['stop_loss'])
                if tighten_stops:
                    # Reduce stop loss (tighter)
                    new_sl = current_sl * (1 - random.uniform(0, factor))
                else:
                    new_sl = current_sl * (1 + random.uniform(-factor, factor))
                risk_mgmt['stop_loss'] = max(0.005, min(0.1, new_sl))  # 0.5% to 10%

            # Mutate take profit
            if 'take_profit' in risk_mgmt:
                current_tp = float(risk_mgmt['take_profit'])
                if improve_sharpe:
                    # Increase take profit for better risk-adjusted returns
                    new_tp = current_tp * (1 + random.uniform(0, factor * 2))
                else:
                    new_tp = current_tp * (1 + random.uniform(-factor, factor))
                risk_mgmt['take_profit'] = max(0.01, min(0.2, new_tp))  # 1% to 20%

            # Mutate position size
            if 'position_size' in risk_mgmt:
                current_size = float(risk_mgmt['position_size'])
                new_size = current_size * (1 + random.uniform(-factor/2, factor/2))
                risk_mgmt['position_size'] = max(0.01, min(0.1, new_size))  # 1% to 10%

            config.risk_management = risk_mgmt

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate risk parameters: {e}")

    async def _mutate_profit_targeting(self, config: StrategyConfig, increase: bool = True):
        """[EVOLVE] Mutate profit targeting strategy"""
        try:
            # Modify exit conditions for better profitability
            new_exit_conditions = []
            for condition in config.exit_conditions:
                if 'profit' in condition.lower():
                    import re
                    profit_match = re.search(r'(\d+)%', condition)
                    if profit_match:
                        current_target = int(profit_match.group(1))
                        if increase:
                            new_target = int(current_target * random.uniform(1.1, 1.5))
                        else:
                            new_target = int(current_target * random.uniform(0.7, 0.9))
                        condition = re.sub(r'\d+%', f'{new_target}%', condition)

                new_exit_conditions.append(condition)

            config.exit_conditions = new_exit_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate profit targeting: {e}")

    async def _mutate_entry_selectivity(self, config: StrategyConfig, more_selective: bool = True):
        """[EVOLVE] Mutate entry condition selectivity"""
        try:
            if more_selective:
                # Add additional filters to make entries more selective
                additional_filters = [
                    "volume > avg_volume_20 * 1.2",
                    "iv_rank < 50",
                    "price_change_1h < 0.02"
                ]

                # Add one random filter
                if len(config.entry_conditions) < 5:  # Don't over-complicate
                    new_filter = random.choice(additional_filters)
                    if new_filter not in config.entry_conditions:
                        config.entry_conditions.append(new_filter)

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate entry selectivity: {e}")

    async def _mutate_risk_reward_ratio(self, config: StrategyConfig):
        """[EVOLVE] Mutate risk/reward ratio for better expectancy"""
        try:
            risk_mgmt = config.risk_management.copy()

            # Aim for 1:2 or 1:3 risk/reward ratio
            if 'stop_loss' in risk_mgmt and 'take_profit' in risk_mgmt:
                stop_loss = float(risk_mgmt['stop_loss'])
                target_ratio = random.uniform(2.0, 3.0)  # 1:2 to 1:3
                new_take_profit = stop_loss * target_ratio

                risk_mgmt['take_profit'] = min(0.2, new_take_profit)  # Cap at 20%
                config.risk_management = risk_mgmt

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate risk/reward ratio: {e}")

    async def _add_random_filter(self, config: StrategyConfig, mutation_config: Dict):
        """[EVOLVE] Add random filter to strategy"""
        try:
            possible_filters = [
                "iv_rank > 30",
                "iv_rank < 70",
                "volume > avg_volume_10 * 1.5",
                "price_change_1d > -0.02",
                "price_change_1d < 0.02",
                "vix < 25",
                "vix > 15",
                "time_to_expiry > 7",
                "time_to_expiry < 45",
                "delta > 0.3",
                "delta < 0.7"
            ]

            # Add filter if not too many conditions already
            if len(config.entry_conditions) < 6:
                new_filter = random.choice(possible_filters)
                if new_filter not in config.entry_conditions:
                    config.entry_conditions.append(new_filter)

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to add random filter: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [EVALUATE] FEATURE 3: STRATEGY EVALUATION PIPELINE INTEGRATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _evaluate_strategies(self):
        """[EVALUATE] Evaluate evolved strategies through backtesting pipeline"""
        while self.is_running:
            try:
                logger.info("[EVALUATE] [EVALUATE] Evaluating evolved strategies...")

                # Find experimental strategies that need evaluation
                experimental_strategies = [
                    s for s in self.strategy_registry.values()
                    if s.status == StrategyStatus.EXPERIMENTAL
                ]

                if experimental_strategies:
                    # Prepare strategies for backtesting
                    await self._prepare_for_backtesting(experimental_strategies)

                    # Trigger backtesting agent
                    await self._trigger_backtesting()

                    # Process results
                    await self._process_backtest_results()

                await asyncio.sleep(self.intervals['full_evolution'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Strategy evaluation failed: {e}")
                await asyncio.sleep(300)

    async def _prepare_for_backtesting(self, strategies: List[StrategyConfig]):
        """[EVALUATE] Prepare strategies for backtesting"""
        try:
            # Convert strategies to backtesting format
            backtest_strategies = []

            for strategy in strategies:
                backtest_format = {
                    'strategy_id': strategy.strategy_id,
                    'name': strategy.name,
                    'description': strategy.description,
                    'parameters': strategy.parameters,
                    'entry_conditions': strategy.entry_conditions,
                    'exit_conditions': strategy.exit_conditions,
                    'risk_management': strategy.risk_management,
                    'timeframe': strategy.timeframe,
                    'tags': strategy.tags + ['evolution_candidate']
                }
                backtest_strategies.append(backtest_format)

            # Save to backtesting input file
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backtest_file = self.strategies_path / f"evolution_strategies_{timestamp}.json"

            async with aiofiles.open(backtest_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(backtest_strategies, indent=2))

            logger.info(f"[EVALUATE] [EVALUATE] Prepared {len(strategies)} strategies for backtesting")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to prepare strategies for backtesting: {e}")

    async def _trigger_backtesting(self):
        """[EVALUATE] Trigger backtesting agent"""
        try:
            # This would integrate with the backtesting agent
            # For now, we'll simulate the trigger
            logger.info("[EVALUATE] [EVALUATE] Triggering backtesting agent...")

            # In a real implementation, this would:
            # 1. Send signal to backtesting agent
            # 2. Wait for completion
            # 3. Monitor progress

            # Simulate backtesting delay
            await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to trigger backtesting: {e}")

    async def _process_backtest_results(self):
        """[EVALUATE] Process backtesting results with chunked optimization"""
        try:
            # Load latest backtest results
            backtest_files = list(self.backtest_path.glob("backtest_results_*.json"))
            if not backtest_files:
                return

            latest_backtest = max(backtest_files, key=lambda x: x.stat().st_mtime)

            async with aiofiles.open(latest_backtest, 'r', encoding='utf-8') as f:
                content = await f.read()
                results = json.loads(content)

            # Filter experimental strategies
            experimental_ids = {
                s.strategy_id for s in self.strategy_registry.values()
                if s.status == StrategyStatus.EXPERIMENTAL
            }
            
            experimental_results = {
                sid: res for sid, res in results.items() if sid in experimental_ids
            }

            if not experimental_results:
                return

            # Process results in parallel and create a list of metrics
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(
                    self.thread_pool,
                    self._process_strategy_result,
                    strategy_id,
                    result
                )
                for strategy_id, result in experimental_results.items()
            ]
            
            processed_metrics = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out failed results
            valid_results = [
                m for m in processed_metrics 
                if m is not None and not isinstance(m, Exception)
            ]

            if not valid_results:
                return

            # Update performance_history dictionary
            for strategy_id, metrics in valid_results:
                self.performance_history[strategy_id].append(metrics)

            # Create DataFrame from new metrics
            new_metrics_list = [metrics.to_dict() for _, metrics in valid_results]
            if not new_metrics_list:
                return
            
            new_metrics_df = pl.DataFrame(new_metrics_list)

            # Append new metrics to the main performance DataFrame
            self.performance_df = self.performance_df.vstack(new_metrics_df)

            # Save updated performance history
            await self._save_performance_history()
            logger.info(f"[EVALUATE] Processed and added {len(new_metrics_df)} new metrics to performance history")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to process backtest results: {e}")
    
    def _process_strategy_result(self, strategy_id: str, result: Dict) -> Tuple[str, StrategyMetrics]:
        """[PERFORMANCE] Process single strategy result synchronously"""
        try:
            # Use vectorized calculations if available
            if VECTORBT_AVAILABLE and 'returns' in result:
                metrics = self._create_metrics_vectorized(strategy_id, result)
            else:
                metrics = self._create_metrics_standard(strategy_id, result)
            
            # Evaluate performance synchronously
            self._evaluate_strategy_performance_sync(strategy_id, metrics)
            
            return strategy_id, metrics
        except Exception as e:
            logger.error(f"[ERROR] Failed to process strategy result for {strategy_id}: {e}")
            return None

    def _create_metrics_vectorized(self, strategy_id: str, result: Dict) -> StrategyMetrics:
        """[PERFORMANCE] Create metrics using vectorized operations"""
        try:
            returns_np = np.array(result.get('returns', []))

            if len(returns_np) > 0:
                # Check if GPU acceleration is enabled and available
                if self.gpu_acceleration and CUPY_AVAILABLE:
                    returns = cp.asarray(returns_np)
                    # Use GPU functions
                    roi = calculate_expectancy_gpu(returns) * returns.size
                    sharpe_ratio = calculate_sharpe_ratio_gpu(returns)
                    win_rate = calculate_win_rate_gpu(returns)
                    max_drawdown = abs(calculate_max_drawdown_gpu(returns))
                    expectancy = calculate_expectancy_gpu(returns)
                    profit_factor = calculate_profit_factor_gpu(returns)
                    volatility = float(returns.std()) if returns.size > 1 else 0
                else:
                    returns = returns_np
                    # Use JIT-compiled functions for performance
                    roi = calculate_expectancy_fast(returns) * len(returns)  # Approximate total return
                    sharpe_ratio = calculate_sharpe_ratio_fast(returns)
                    win_rate = calculate_win_rate_fast(returns)
                    max_drawdown = abs(calculate_max_drawdown_fast(returns))
                    expectancy = calculate_expectancy_fast(returns)
                    profit_factor = calculate_profit_factor_fast(returns)
                    volatility = np.std(returns) if len(returns) > 1 else 0

                # Calculate additional metrics
                calmar_ratio = roi / max_drawdown if max_drawdown > 0 else 0
                sortino_ratio = self._calculate_sortino_ratio(returns_np)
            else:
                roi = sharpe_ratio = win_rate = max_drawdown = expectancy = 0
                profit_factor = volatility = calmar_ratio = sortino_ratio = 0

            return StrategyMetrics(
                strategy_id=strategy_id,
                roi=roi,
                sharpe_ratio=sharpe_ratio,
                win_rate=win_rate,
                max_drawdown=max_drawdown,
                expectancy=expectancy,
                profit_factor=profit_factor,
                total_trades=result.get('total_trades', len(returns)),
                avg_trade_duration=result.get('avg_trade_duration', 0),
                volatility=volatility,
                calmar_ratio=calmar_ratio,
                sortino_ratio=sortino_ratio,
                timestamp=datetime.now(),
                regime=self.market_regime_cache or MarketRegime.SIDEWAYS_LOW_VOL
            )
        except Exception as e:
            logger.error(f"[ERROR] Failed to create vectorized metrics: {e}")
            return self._create_metrics_standard(strategy_id, result)

    def _create_metrics_standard(self, strategy_id: str, result: Dict) -> StrategyMetrics:
        """[PERFORMANCE] Create metrics using standard method"""
        return StrategyMetrics(
            strategy_id=strategy_id,
            roi=result.get('total_return', 0),
            sharpe_ratio=result.get('sharpe_ratio', 0),
            win_rate=result.get('win_rate', 0),
            max_drawdown=abs(result.get('max_drawdown', 0)),
            expectancy=result.get('expectancy', 0),
            profit_factor=result.get('profit_factor', 1),
            total_trades=result.get('total_trades', 0),
            avg_trade_duration=result.get('avg_trade_duration', 0),
            volatility=result.get('volatility', 0),
            calmar_ratio=result.get('calmar_ratio', 0),
            sortino_ratio=result.get('sortino_ratio', 0),
            timestamp=datetime.now(),
            regime=self.market_regime_cache or MarketRegime.SIDEWAYS_LOW_VOL
        )

    async def _evaluate_strategy_performance(self, strategy_id: str, metrics: StrategyMetrics):
        """[EVALUATE] Evaluate strategy performance and decide on promotion/demotion"""
        try:
            strategy = self.strategy_registry[strategy_id]

            # Compare with parent strategy if available
            parent_performance = None
            if strategy.parent_id:
                parent_df = self.performance_df.filter(pl.col("strategy_id") == strategy.parent_id).sort("timestamp").tail(1)
                if not parent_df.is_empty():
                    parent_performance = parent_df.to_dicts()[0]

            # Evaluation criteria
            meets_thresholds = (
                metrics.roi >= self.performance_thresholds['min_roi'] and
                metrics.sharpe_ratio >= self.performance_thresholds['min_sharpe'] and
                metrics.win_rate >= self.performance_thresholds['min_win_rate'] and
                metrics.max_drawdown <= self.performance_thresholds['max_drawdown'] and
                metrics.expectancy >= self.performance_thresholds['min_expectancy'] and
                metrics.total_trades >= self.performance_thresholds['min_trades']
            )

            # Compare with parent
            better_than_parent = True
            if parent_performance:
                improvement_score = (
                    (metrics.roi - parent_performance['roi']) * 0.3 +
                    (metrics.sharpe_ratio - parent_performance['sharpe_ratio']) * 0.3 +
                    (metrics.win_rate - parent_performance['win_rate']) * 0.2 +
                    (parent_performance['max_drawdown'] - metrics.max_drawdown) * 0.2
                )
                better_than_parent = improvement_score > 0.01  # At least 1% improvement

            # Decision logic
            if meets_thresholds and better_than_parent:
                # Promote strategy
                strategy.status = StrategyStatus.PROMOTED
                await self._send_notification(
                    f"🎉 Strategy Promoted: {strategy_id}",
                    f"ROI: {metrics.roi:.2%}, Sharpe: {metrics.sharpe_ratio:.2f}, Win Rate: {metrics.win_rate:.2%}"
                )

                # Demote parent if significantly outperformed
                if parent_performance and better_than_parent:
                    parent_strategy = self.strategy_registry.get(strategy.parent_id)
                    if parent_strategy and parent_strategy.status == StrategyStatus.ACTIVE:
                        parent_strategy.status = StrategyStatus.DEPRECATED

            elif not meets_thresholds:
                # Mark as failed
                strategy.status = StrategyStatus.DISABLED

            else:
                # Keep experimental for more testing
                pass

            # Log evaluation
            await self._log_evolution_event(
                strategy_id=strategy_id,
                reason=EvolutionReason.SCHEDULED_OPTIMIZATION,
                changes={'evaluation_result': strategy.status.value},
                description=f"Strategy evaluation completed: {strategy.status.value}",
                metrics_after=metrics
            )

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to evaluate strategy performance: {e}")

    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """[PERFORMANCE] Calculate Sortino ratio using vectorized operations"""
        if returns.size == 0:
            return 0.0

        # This calculation remains on the CPU as it's less intensive
        # and avoids GPU-CPU data transfer for a small part of the calculation.
        downside_returns = returns[returns < 0]
        if downside_returns.size == 0:
            return float('inf') if np.mean(returns) > 0 else 0.0

        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 0.0

        return np.mean(returns) / downside_deviation

    def _evaluate_strategy_performance_sync(self, strategy_id: str, metrics: StrategyMetrics) -> None:
        """[EVALUATE] Synchronous performance evaluation for parallel execution"""
        try:
            strategy = self.strategy_registry[strategy_id]

            # Compare with parent strategy if available
            parent_performance = None
            if strategy.parent_id and strategy.parent_id in self.performance_history:
                parent_history = self.performance_history[strategy.parent_id]
                if parent_history:
                    parent_performance = parent_history[-1]  # Latest performance

            # Evaluation criteria
            meets_thresholds = (
                metrics.roi >= self.performance_thresholds['min_roi'] and
                metrics.sharpe_ratio >= self.performance_thresholds['min_sharpe'] and
                metrics.win_rate >= self.performance_thresholds['min_win_rate'] and
                metrics.max_drawdown <= self.performance_thresholds['max_drawdown'] and
                metrics.expectancy >= self.performance_thresholds['min_expectancy'] and
                metrics.total_trades >= self.performance_thresholds['min_trades']
            )

            # Compare with parent
            better_than_parent = True
            if parent_performance:
                improvement_score = (
                    (metrics.roi - parent_performance.roi) * 0.3 +
                    (metrics.sharpe_ratio - parent_performance.sharpe_ratio) * 0.3 +
                    (metrics.win_rate - parent_performance.win_rate) * 0.2 +
                    (parent_performance.max_drawdown - metrics.max_drawdown) * 0.2
                )
                better_than_parent = improvement_score > 0.01  # At least 1% improvement

            # Decision logic
            if meets_thresholds and better_than_parent:
                strategy.status = StrategyStatus.PROMOTED
                # Demote parent if significantly outperformed
                if parent_performance and better_than_parent:
                    parent_strategy = self.strategy_registry.get(strategy.parent_id)
                    if parent_strategy and parent_strategy.status == StrategyStatus.ACTIVE:
                        parent_strategy.status = StrategyStatus.DEPRECATED
            elif not meets_thresholds:
                strategy.status = StrategyStatus.DISABLED
            # else: keep experimental for more testing

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to evaluate strategy performance: {e}")

    # Add synchronous helper methods for mutations
    def _mutate_rsi_parameters_sync(self, config: StrategyConfig, mutation_config: Dict, factor: float):
        """[EVOLVE] Synchronous RSI parameter mutation"""
        try:
            rsi_range = mutation_config.get('rsi_range', [5, 25])

            # Update entry conditions
            new_conditions = []
            for condition in config.entry_conditions:
                if 'rsi' in condition.lower():
                    # Extract current RSI value and modify
                    import re
                    rsi_match = re.search(r'rsi[_\(](\d+)', condition.lower())
                    if rsi_match:
                        current_period = int(rsi_match.group(1))
                        new_period = max(rsi_range[0], min(rsi_range[1],
                                       int(current_period * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'rsi[_\(](\d+)', f'rsi_{new_period}', condition, flags=re.IGNORECASE)

                    # Modify threshold values
                    threshold_match = re.search(r'[><=]\s*(\d+)', condition)
                    if threshold_match:
                        current_threshold = int(threshold_match.group(1))
                        new_threshold = max(10, min(90,
                                          int(current_threshold * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'([><=]\s*)(\d+)', f'\\g<1>{new_threshold}', condition)

                new_conditions.append(condition)

            config.entry_conditions = new_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate RSI parameters: {e}")

    def _mutate_ma_parameters_sync(self, config: StrategyConfig, mutation_config: Dict, factor: float):
        """[EVOLVE] Synchronous MA parameter mutation"""
        try:
            ma_range = mutation_config.get('ma_range', [5, 50])

            # Update entry conditions
            new_conditions = []
            for condition in config.entry_conditions:
                if any(ma_type in condition.lower() for ma_type in ['ma', 'ema', 'sma']):
                    # Extract and modify MA periods
                    import re
                    ma_match = re.search(r'(ma|ema|sma)[_\(](\d+)', condition.lower())
                    if ma_match:
                        ma_type = ma_match.group(1)
                        current_period = int(ma_match.group(2))
                        new_period = max(ma_range[0], min(ma_range[1],
                                       int(current_period * (1 + random.uniform(-factor, factor)))))
                        condition = re.sub(r'(ma|ema|sma)[_\(](\d+)', f'{ma_type}_{new_period}',
                                         condition, flags=re.IGNORECASE)

                new_conditions.append(condition)

            config.entry_conditions = new_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate MA parameters: {e}")

    def _mutate_risk_parameters_sync(self, config: StrategyConfig, factor: float,
                                    improve_sharpe: bool = False, tighten_stops: bool = False):
        """[EVOLVE] Synchronous risk parameter mutation"""
        try:
            risk_mgmt = config.risk_management.copy()

            # Mutate stop loss
            if 'stop_loss' in risk_mgmt:
                current_sl = float(risk_mgmt['stop_loss'])
                if tighten_stops:
                    # Reduce stop loss (tighter)
                    new_sl = current_sl * (1 - random.uniform(0, factor))
                else:
                    new_sl = current_sl * (1 + random.uniform(-factor, factor))
                risk_mgmt['stop_loss'] = max(0.005, min(0.1, new_sl))  # 0.5% to 10%

            # Mutate take profit
            if 'take_profit' in risk_mgmt:
                current_tp = float(risk_mgmt['take_profit'])
                if improve_sharpe:
                    # Increase take profit for better risk-adjusted returns
                    new_tp = current_tp * (1 + random.uniform(0, factor * 2))
                else:
                    new_tp = current_tp * (1 + random.uniform(-factor, factor))
                risk_mgmt['take_profit'] = max(0.01, min(0.2, new_tp))  # 1% to 20%

            # Mutate position size
            if 'position_size' in risk_mgmt:
                current_size = float(risk_mgmt['position_size'])
                new_size = current_size * (1 + random.uniform(-factor/2, factor/2))
                risk_mgmt['position_size'] = max(0.01, min(0.1, new_size))  # 1% to 10%

            config.risk_management = risk_mgmt

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate risk parameters: {e}")

    def _mutate_profit_targeting_sync(self, config: StrategyConfig, increase: bool = True):
        """[EVOLVE] Synchronous profit targeting mutation"""
        try:
            # Modify exit conditions for better profitability
            new_exit_conditions = []
            for condition in config.exit_conditions:
                if 'profit' in condition.lower():
                    import re
                    profit_match = re.search(r'(\d+)%', condition)
                    if profit_match:
                        current_target = int(profit_match.group(1))
                        if increase:
                            new_target = int(current_target * random.uniform(1.1, 1.5))
                        else:
                            new_target = int(current_target * random.uniform(0.7, 0.9))
                        condition = re.sub(r'\d+%', f'{new_target}%', condition)

                new_exit_conditions.append(condition)

            config.exit_conditions = new_exit_conditions

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate profit targeting: {e}")

    def _mutate_entry_selectivity_sync(self, config: StrategyConfig, more_selective: bool = True):
        """[EVOLVE] Synchronous entry selectivity mutation"""
        try:
            if more_selective:
                # Add additional filters to make entries more selective
                additional_filters = [
                    "volume > avg_volume_20 * 1.2",
                    "iv_rank < 50",
                    "price_change_1h < 0.02"
                ]

                # Add one random filter
                if len(config.entry_conditions) < 5:  # Don't over-complicate
                    new_filter = random.choice(additional_filters)
                    if new_filter not in config.entry_conditions:
                        config.entry_conditions.append(new_filter)

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate entry selectivity: {e}")

    def _mutate_risk_reward_ratio_sync(self, config: StrategyConfig):
        """[EVOLVE] Synchronous risk/reward ratio mutation"""
        try:
            risk_mgmt = config.risk_management.copy()

            # Aim for 1:2 or 1:3 risk/reward ratio
            if 'stop_loss' in risk_mgmt and 'take_profit' in risk_mgmt:
                stop_loss = float(risk_mgmt['stop_loss'])
                target_ratio = random.uniform(2.0, 3.0)  # 1:2 to 1:3
                new_take_profit = stop_loss * target_ratio

                risk_mgmt['take_profit'] = min(0.2, new_take_profit)  # Cap at 20%
                config.risk_management = risk_mgmt

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to mutate risk/reward ratio: {e}")

    def _update_memory_usage(self):
        """[PERFORMANCE] Update memory usage tracking"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            self.memory_usage_mb = process.memory_info().rss / 1024 / 1024
        except ImportError:
            # Fallback estimation
            import sys
            self.memory_usage_mb = sys.getsizeof(self.strategy_registry) / 1024 / 1024
    
    def _update_cache_hit_rate(self):
        """[PERFORMANCE] Update cache hit rate"""
        total_requests = self.cache_hits + self.cache_misses
        if total_requests > 0:
            self.cache_hit_rate = self.cache_hits / total_requests
    
    async def _cleanup_memory(self):
        """[PERFORMANCE] Periodic memory cleanup"""
        try:
            import gc
            
            # Clear old cache entries
            if len(self.performance_cache) > 500:
                # Keep only recent entries
                sorted_keys = sorted(self.performance_cache.keys())
                old_keys = sorted_keys[:-250]  # Keep last 250
                for key in old_keys:
                    del self.performance_cache[key]
            
            # Clear computation cache
            if len(self.computation_cache) > 200:
                self.computation_cache.clear()
            
            # Limit performance history size
            for strategy_id in list(self.performance_history.keys()):
                if len(self.performance_history[strategy_id]) > 100:
                    # Keep only last 50 entries
                    self.performance_history[strategy_id] = self.performance_history[strategy_id][-50:]
            
            # Force garbage collection
            gc.collect()
            
            self.last_memory_cleanup = asyncio.get_event_loop().time()
            
        except Exception as e:
            logger.error(f"[ERROR] Memory cleanup failed: {e}")
    
    async def cleanup(self):
        """Cleanup resources with optimized shutdown"""
        try:
            self._safe_log("INFO", "[CLEANUP] Cleaning up Options Strategy Evolution Agent...")
            self.is_running = False

            # Final memory cleanup
            await self._cleanup_memory()
            
            # Clear caches
            self.performance_cache.clear()
            self.computation_cache.clear()

            # Shutdown executors
            if hasattr(self, 'thread_pool'):
                self.thread_pool.shutdown(wait=True)
            if hasattr(self, 'process_pool'):
                self.process_pool.shutdown(wait=True)

            # Save all data before cleanup
            await self._save_strategy_registry()
            await self._save_performance_history()
            await self._save_evolution_history()

            self._safe_log("INFO", "[SUCCESS] Options Strategy Evolution Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """[EFFICIENCY] Get performance summary for monitoring"""
        self._update_memory_usage()
        return {
            'runtime_minutes': (asyncio.get_event_loop().time() - getattr(self, 'start_time', 0)) / 60,
            'total_strategies': len(self.strategy_registry),
            'performance_checks': self.performance_check_count,
            'evolution_cycles': self.evolution_cycle_count,
            'cache_size': len(self.performance_cache),
            'cache_hit_rate': self.cache_hit_rate,
            'memory_usage_mb': self.memory_usage_mb,
            'active_strategies': len([s for s in self.strategy_registry.values() if s.status == StrategyStatus.ACTIVE]),
            'experimental_strategies': len([s for s in self.strategy_registry.values() if s.status == StrategyStatus.EXPERIMENTAL]),
            'performance_history_size': sum(len(h) for h in self.performance_history.values())
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # [PROMOTION] FEATURE 4: AUTOMATED PROMOTION/DEMOTION DECISIONS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _manage_promotions_demotions(self):
        """[PROMOTION] Manage strategy promotions and demotions"""
        while self.is_running:
            try:
                logger.info("[PROMOTION] [PROMOTION] Managing strategy promotions/demotions...")

                # Review promoted strategies
                await self._review_promoted_strategies()

                # Review active strategies for potential demotion
                await self._review_active_strategies()

                # Clean up disabled strategies
                await self._cleanup_disabled_strategies()

                await asyncio.sleep(self.intervals['full_evolution'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Promotion/demotion management failed: {e}")
                await asyncio.sleep(300)

    async def _review_promoted_strategies(self):
        """[PROMOTION] Review promoted strategies for activation"""
        try:
            promoted_strategies = [
                s for s in self.strategy_registry.values()
                if s.status == StrategyStatus.PROMOTED
            ]

            for strategy in promoted_strategies:
                # Check if strategy has sufficient performance history
                if strategy.strategy_id in self.performance_history:
                    history = self.performance_history[strategy.strategy_id]

                    if len(history) >= 3:  # At least 3 performance measurements
                        # Calculate average performance
                        avg_roi = statistics.mean([m.roi for m in history[-3:]])
                        avg_sharpe = statistics.mean([m.sharpe_ratio for m in history[-3:]])
                        avg_win_rate = statistics.mean([m.win_rate for m in history[-3:]])

                        # Check consistency
                        if (avg_roi >= self.performance_thresholds['min_roi'] and
                            avg_sharpe >= self.performance_thresholds['min_sharpe'] and
                            avg_win_rate >= self.performance_thresholds['min_win_rate']):

                            # Activate strategy (keep as PROMOTED status for production use)
                            strategy.status = StrategyStatus.ACTIVE

                            # Ensure it's exported to production if it meets criteria
                            if await self._is_strategy_ready_for_production(strategy.strategy_id, strategy):
                                await self._export_strategies_to_yaml([strategy.strategy_id])

                            await self._send_notification(
                                f"[SUCCESS] Strategy Activated: {strategy.strategy_id}",
                                f"Consistent performance confirmed. Now active in portfolio."
                            )

                            logger.info(f"[SUCCESS] [PROMOTION] Strategy {strategy.strategy_id} activated")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to review promoted strategies: {e}")

    async def _review_active_strategies(self):
        """[PROMOTION] Review active strategies for potential demotion"""
        try:
            active_strategies = [
                s for s in self.strategy_registry.values()
                if s.status == StrategyStatus.ACTIVE
            ]

            for strategy in active_strategies:
                if strategy.strategy_id in self.performance_history:
                    history = self.performance_history[strategy.strategy_id]

                    if len(history) >= 5:  # Need sufficient history
                        # Check recent performance trend
                        recent_metrics = history[-3:]  # Last 3 measurements

                        # Calculate performance degradation
                        roi_declining = all(
                            recent_metrics[i].roi < recent_metrics[i-1].roi
                            for i in range(1, len(recent_metrics))
                        )

                        sharpe_declining = all(
                            recent_metrics[i].sharpe_ratio < recent_metrics[i-1].sharpe_ratio
                            for i in range(1, len(recent_metrics))
                        )

                        # Check if below thresholds
                        latest_metrics = recent_metrics[-1]
                        below_thresholds = (
                            latest_metrics.roi < self.performance_thresholds['min_roi'] or
                            latest_metrics.sharpe_ratio < self.performance_thresholds['min_sharpe'] or
                            latest_metrics.max_drawdown > self.performance_thresholds['max_drawdown']
                        )

                        if (roi_declining and sharpe_declining) or below_thresholds:
                            # Use the new demotion method for proper lifecycle management
                            reason = "performance_decline" if (roi_declining and sharpe_declining) else "below_thresholds"
                            await self.demote_strategy_from_production(strategy.strategy_id, reason)

                            await self._send_notification(
                                f"⬇️ Strategy Demoted: {strategy.strategy_id}",
                                f"Performance declining. Moved to deprecated status."
                            )

                            logger.info(f"⬇️ [DEMOTION] Strategy {strategy.strategy_id} demoted")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to review active strategies: {e}")

    async def _cleanup_disabled_strategies(self):
        """[PROMOTION] Clean up old disabled strategies"""
        try:
            disabled_strategies = [
                s for s in self.strategy_registry.values()
                if s.status == StrategyStatus.DISABLED
            ]

            # Remove strategies disabled for more than 30 days
            cutoff_date = datetime.now() - timedelta(days=30)

            for strategy in disabled_strategies:
                if strategy.created_at < cutoff_date:
                    # Archive strategy data
                    await self._archive_strategy(strategy.strategy_id)

                    # Remove from registry
                    del self.strategy_registry[strategy.strategy_id]

                    logger.info(f"🗑️ [CLEANUP] Archived and removed disabled strategy {strategy.strategy_id}")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to cleanup disabled strategies: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [REGIME] FEATURE 5: MARKET-REGIME ADAPTATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _adapt_to_market_regime(self):
        """[REGIME] Adapt strategies to market regime changes"""
        while self.is_running:
            try:
                logger.info("[REGIME] [REGIME] Checking market regime adaptation...")

                # Detect current market regime
                current_regime = await self._detect_current_market_regime()

                if current_regime != self.market_regime_cache:
                    logger.info(f"[REGIME] [REGIME] Market regime changed: {self.market_regime_cache} → {current_regime}")

                    # Update cache
                    previous_regime = self.market_regime_cache
                    self.market_regime_cache = current_regime

                    # Adapt strategies to new regime
                    await self._adapt_strategies_to_regime(current_regime, previous_regime)

                    await self._send_notification(
                        f"[REGIME] Market Regime Change Detected",
                        f"Regime changed from {previous_regime.value if previous_regime else 'unknown'} to {current_regime.value}"
                    )

                await asyncio.sleep(self.intervals['regime_adaptation'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Market regime adaptation failed: {e}")
                await asyncio.sleep(300)

# 💾 Data persistence methods
    async def _save_strategy_registry(self):
        """💾 Save strategy registry to file"""
        try:
            registry_data = {}
            for strategy_id, strategy in self.strategy_registry.items():
                registry_data[strategy_id] = strategy.to_dict()

            registry_file = self.registry_path / "strategy_registry.json"
            async with aiofiles.open(registry_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(registry_data, indent=2, default=str))

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to save strategy registry: {e}")

    async def _save_performance_history(self):
        """💾 Save performance history to optimized Parquet file"""
        try:
            if self.performance_df.is_empty():
                return

            # Save to Parquet format for speed and smaller file size
            performance_file = self.evolution_path / "performance_history.parquet"
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.thread_pool,
                lambda: self.performance_df.write_parquet(performance_file)
            )

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to save performance history: {e}")

    async def _save_evolution_history(self):
        """💾 Save evolution history to file"""
        try:
            history_data = [event.to_dict() for event in self.evolution_history]

            evolution_file = self.evolution_path / "evolution_history.json"
            async with aiofiles.open(evolution_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(history_data, indent=2, default=str))

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to save evolution history: {e}")

    async def _export_strategies_to_yaml(self, strategy_ids: List[str] = None):
        """🔄 Export evolved strategies to config/options_strategies.yaml format"""
        try:
            logger.info("[EXPORT] Exporting evolved strategies to YAML configuration...")

            # Load current YAML configuration
            yaml_config_path = Path("config/options_strategies.yaml")
            current_config = {}

            if yaml_config_path.exists():
                async with aiofiles.open(yaml_config_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    current_config = yaml.safe_load(content) or {}

            # Get strategies to export (all if none specified)
            strategies_to_export = strategy_ids or list(self.strategy_registry.keys())

            # Filter for successful/promoted strategies only
            successful_strategies = []
            for strategy_id in strategies_to_export:
                if strategy_id in self.strategy_registry:
                    strategy = self.strategy_registry[strategy_id]
                    # Only export strategies that are active or promoted
                    if strategy.status in [StrategyStatus.ACTIVE, StrategyStatus.PROMOTED]:
                        successful_strategies.append(strategy)

            if not successful_strategies:
                logger.info("[EXPORT] No successful strategies to export")
                return False

            # Convert evolved strategies to YAML format
            exported_count = 0
            if 'strategies' not in current_config:
                current_config['strategies'] = {}

            for strategy in successful_strategies:
                yaml_strategy = await self._convert_strategy_to_yaml_format(strategy)
                if yaml_strategy:
                    # Use strategy name as key, make it unique if needed
                    strategy_key = self._generate_unique_strategy_key(strategy.name, current_config['strategies'])
                    current_config['strategies'][strategy_key] = yaml_strategy
                    exported_count += 1
                    logger.info(f"[EXPORT] Exported strategy: {strategy_key}")

            # Save updated configuration
            if exported_count > 0:
                # Create backup of original file
                backup_path = yaml_config_path.with_suffix('.yaml.backup')
                if yaml_config_path.exists():
                    import shutil
                    shutil.copy2(yaml_config_path, backup_path)
                    logger.info(f"[EXPORT] Created backup: {backup_path}")

                # Save updated configuration
                async with aiofiles.open(yaml_config_path, 'w', encoding='utf-8') as f:
                    yaml_content = yaml.dump(current_config, default_flow_style=False, indent=2, sort_keys=False)
                    await f.write(yaml_content)

                logger.info(f"[SUCCESS] Exported {exported_count} evolved strategies to {yaml_config_path}")

                # Send notification
                await self._send_notification(
                    f"Strategy Export Complete",
                    f"Exported {exported_count} evolved strategies to production configuration"
                )

                return True
            else:
                logger.info("[EXPORT] No strategies were exported")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies to YAML: {e}")
            return False

    async def _convert_strategy_to_yaml_format(self, strategy: StrategyConfig) -> Dict[str, Any]:
        """🔄 Convert StrategyConfig to YAML format compatible with options_strategies.yaml"""
        try:
            # Get recent performance metrics for this strategy
            recent_metrics = None
            if strategy.strategy_id in self.performance_history:
                metrics_list = self.performance_history[strategy.strategy_id]
                if metrics_list:
                    recent_metrics = metrics_list[-1]  # Most recent

            # Determine strategy type based on parameters and conditions
            strategy_type = self._infer_strategy_type(strategy)

            # Convert entry conditions to YAML format
            entry_conditions = []
            for condition in strategy.entry_conditions:
                if isinstance(condition, str):
                    entry_conditions.append(condition)
                elif isinstance(condition, dict):
                    # Convert dict conditions to string format
                    for key, value in condition.items():
                        entry_conditions.append(f"{key} {value}")

            # Build YAML strategy structure
            yaml_strategy = {
                'name': strategy.name,
                'type': strategy_type,
                'description': strategy.description or f"Evolved strategy from {strategy.parent_id or 'base'}",
                'market_outlook': strategy.market_outlook,
                'volatility_outlook': strategy.volatility_outlook,
                'timeframes': [strategy.timeframe] if strategy.timeframe else ["5min", "15min"],
                'underlyings': ["NIFTY", "BANKNIFTY"],  # Default underlyings
                'parameters': {
                    'entry_conditions': entry_conditions,
                    'confidence_threshold': strategy.parameters.get('confidence_threshold', 0.4),
                    'max_signals_per_day': strategy.parameters.get('max_signals_per_day', 5)
                }
            }

            # Add entry conditions at root level for compatibility
            if entry_conditions:
                yaml_strategy['entry_conditions'] = {}
                for condition in entry_conditions:
                    if '>' in condition:
                        key, value = condition.split('>', 1)
                        yaml_strategy['entry_conditions'][key.strip()] = f"> {value.strip()}"
                    elif '<' in condition:
                        key, value = condition.split('<', 1)
                        yaml_strategy['entry_conditions'][key.strip()] = f"< {value.strip()}"
                    elif '=' in condition:
                        key, value = condition.split('=', 1)
                        yaml_strategy['entry_conditions'][key.strip()] = value.strip()

            # Add exit conditions if available
            if strategy.exit_conditions:
                yaml_strategy['exit_conditions'] = {}
                for condition in strategy.exit_conditions:
                    if isinstance(condition, str):
                        if 'profit_target' in condition:
                            yaml_strategy['exit_conditions']['profit_target'] = 0.5
                        elif 'stop_loss' in condition:
                            yaml_strategy['exit_conditions']['stop_loss'] = 0.3
                        elif 'time_exit' in condition:
                            yaml_strategy['exit_conditions']['time_exit'] = "15:15"

            # Add risk management
            if strategy.risk_management:
                yaml_strategy['risk_management'] = strategy.risk_management
            else:
                yaml_strategy['risk_management'] = {
                    'max_loss_per_trade': 1000,
                    'position_size_pct': 0.02,
                    'max_concurrent_positions': 3
                }

            # Add performance metadata as comments (will be preserved in YAML)
            if recent_metrics:
                yaml_strategy['_performance_metadata'] = {
                    'last_roi': round(recent_metrics.roi, 4),
                    'last_sharpe': round(recent_metrics.sharpe_ratio, 2),
                    'last_win_rate': round(recent_metrics.win_rate, 3),
                    'last_updated': recent_metrics.timestamp.isoformat(),
                    'evolution_version': strategy.version,
                    'parent_strategy': strategy.parent_id
                }

            return yaml_strategy

        except Exception as e:
            logger.error(f"[ERROR] Failed to convert strategy {strategy.strategy_id} to YAML format: {e}")
            return None

    def _infer_strategy_type(self, strategy: StrategyConfig) -> str:
        """🔍 Infer strategy type from strategy configuration"""
        try:
            # Check strategy name and description for type hints
            name_lower = strategy.name.lower()
            desc_lower = (strategy.description or "").lower()

            # Check for specific strategy types
            if any(keyword in name_lower for keyword in ['straddle', 'strangle']):
                return 'volatility'
            elif any(keyword in name_lower for keyword in ['spread', 'bull', 'bear']):
                return 'spread'
            elif any(keyword in name_lower for keyword in ['momentum', 'trend']):
                return 'directional'
            elif any(keyword in name_lower for keyword in ['mean', 'reversion', 'oversold', 'overbought']):
                return 'mean_reversion'
            elif any(keyword in name_lower for keyword in ['volume', 'flow']):
                return 'flow'
            elif any(keyword in name_lower for keyword in ['iron', 'condor']):
                return 'iron_condor'

            # Check market outlook
            if strategy.market_outlook == 'bullish':
                return 'directional'
            elif strategy.market_outlook == 'bearish':
                return 'directional'
            elif strategy.market_outlook == 'neutral':
                return 'volatility'

            # Default based on volatility outlook
            if strategy.volatility_outlook in ['high', 'expanding']:
                return 'volatility'
            else:
                return 'directional'

        except Exception as e:
            logger.error(f"[ERROR] Failed to infer strategy type: {e}")
            return 'directional'  # Default fallback

    def _generate_unique_strategy_key(self, base_name: str, existing_strategies: Dict) -> str:
        """🔑 Generate unique strategy key for YAML configuration"""
        try:
            # Clean the base name
            clean_name = base_name.lower().replace(' ', '_').replace('-', '_')
            clean_name = ''.join(c for c in clean_name if c.isalnum() or c == '_')

            # Add evolved suffix
            base_key = f"{clean_name}_evolved"

            # Make it unique
            if base_key not in existing_strategies:
                return base_key

            # Add counter if needed
            counter = 1
            while f"{base_key}_{counter}" in existing_strategies:
                counter += 1

            return f"{base_key}_{counter}"

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate unique strategy key: {e}")
            return f"evolved_strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    async def _sync_evolved_strategies_to_production(self):
        """🔄 Sync successful evolved strategies to production configuration"""
        try:
            logger.info("[SYNC] Syncing evolved strategies to production...")

            # Find strategies that should be promoted to production
            promotion_candidates = []

            for strategy_id, strategy in self.strategy_registry.items():
                # Check if strategy is ready for production
                if await self._is_strategy_ready_for_production(strategy_id, strategy):
                    promotion_candidates.append(strategy_id)

            if not promotion_candidates:
                logger.info("[SYNC] No strategies ready for production promotion")
                return False

            # Export successful strategies to YAML
            success = await self._export_strategies_to_yaml(promotion_candidates)

            if success:
                # Mark strategies as promoted
                for strategy_id in promotion_candidates:
                    if strategy_id in self.strategy_registry:
                        self.strategy_registry[strategy_id].status = StrategyStatus.PROMOTED
                        logger.info(f"[SYNC] Promoted strategy {strategy_id} to production")

                # Save updated registry
                await self._save_strategy_registry()

                # Log promotion event
                await self._log_evolution_event(
                    strategy_id="batch_promotion",
                    reason=EvolutionReason.SCHEDULED_OPTIMIZATION,
                    changes={'promoted_strategies': promotion_candidates},
                    description=f"Promoted {len(promotion_candidates)} strategies to production"
                )

                logger.info(f"[SUCCESS] Synced {len(promotion_candidates)} strategies to production")
                return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to sync evolved strategies to production: {e}")
            return False

    async def _is_strategy_ready_for_production(self, strategy_id: str, strategy: StrategyConfig) -> bool:
        """✅ Check if a strategy is ready for production use"""
        try:
            # Skip if already promoted or deprecated
            if strategy.status in [StrategyStatus.PROMOTED, StrategyStatus.DEPRECATED, StrategyStatus.DISABLED]:
                return False

            # Must have performance history
            if strategy_id not in self.performance_history:
                return False

            metrics_list = self.performance_history[strategy_id]
            if len(metrics_list) < 3:  # Need at least 3 performance measurements
                return False

            # Get recent performance (last 5 measurements)
            recent_metrics = metrics_list[-5:]

            # Calculate average performance
            avg_roi = sum(m.roi for m in recent_metrics) / len(recent_metrics)
            avg_sharpe = sum(m.sharpe_ratio for m in recent_metrics) / len(recent_metrics)
            avg_win_rate = sum(m.win_rate for m in recent_metrics) / len(recent_metrics)
            max_drawdown = max(abs(m.max_drawdown) for m in recent_metrics)

            # Check performance thresholds (higher than minimum for production)
            production_thresholds = {
                'min_roi': self.performance_thresholds['min_roi'] * 1.5,  # 50% higher than minimum
                'min_sharpe': self.performance_thresholds['min_sharpe'] * 1.2,  # 20% higher
                'min_win_rate': self.performance_thresholds['min_win_rate'] * 1.1,  # 10% higher
                'max_drawdown': self.performance_thresholds['max_drawdown'] * 0.8  # 20% lower (stricter)
            }

            # All criteria must be met
            criteria_met = (
                avg_roi >= production_thresholds['min_roi'] and
                avg_sharpe >= production_thresholds['min_sharpe'] and
                avg_win_rate >= production_thresholds['min_win_rate'] and
                max_drawdown <= production_thresholds['max_drawdown']
            )

            if criteria_met:
                logger.info(f"[PROMOTION] Strategy {strategy_id} meets production criteria: "
                          f"ROI={avg_roi:.3f}, Sharpe={avg_sharpe:.2f}, WinRate={avg_win_rate:.3f}, MaxDD={max_drawdown:.3f}")

            return criteria_met

        except Exception as e:
            logger.error(f"[ERROR] Failed to check production readiness for {strategy_id}: {e}")
            return False

    async def _sync_strategies_to_production(self):
        """🔄 Continuous synchronization of evolved strategies to production"""
        while self.is_running:
            try:
                logger.info("[SYNC] Running production synchronization cycle...")

                # Sync evolved strategies to production configuration
                await self._sync_evolved_strategies_to_production()

                # Clean up deprecated strategies from production config
                await self._cleanup_deprecated_strategies()

                # Wait for next sync cycle (every 2 hours)
                await asyncio.sleep(self.intervals.get('production_sync', 7200))

            except Exception as e:
                logger.error(f"[ERROR] Production synchronization failed: {e}")
                await asyncio.sleep(1800)  # Wait 30 minutes before retrying

    async def _cleanup_deprecated_strategies(self):
        """🧹 Remove deprecated strategies from production configuration"""
        try:
            yaml_config_path = Path("config/options_strategies.yaml")
            if not yaml_config_path.exists():
                return

            # Load current configuration
            async with aiofiles.open(yaml_config_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                config = yaml.safe_load(content) or {}

            if 'strategies' not in config:
                return

            # Find strategies to remove (those marked as deprecated in evolution registry)
            strategies_to_remove = []
            for strategy_key, strategy_config in config['strategies'].items():
                # Check if this is an evolved strategy that's now deprecated
                metadata = strategy_config.get('_performance_metadata', {})
                if metadata and 'parent_strategy' in metadata:
                    # This is an evolved strategy, check if it's deprecated
                    for strategy_id, strategy in self.strategy_registry.items():
                        if (strategy.name.lower().replace(' ', '_') in strategy_key and
                            strategy.status == StrategyStatus.DEPRECATED):
                            strategies_to_remove.append(strategy_key)
                            break

            # Remove deprecated strategies
            removed_count = 0
            for strategy_key in strategies_to_remove:
                if strategy_key in config['strategies']:
                    del config['strategies'][strategy_key]
                    removed_count += 1
                    logger.info(f"[CLEANUP] Removed deprecated strategy: {strategy_key}")

            # Save updated configuration if changes were made
            if removed_count > 0:
                async with aiofiles.open(yaml_config_path, 'w', encoding='utf-8') as f:
                    yaml_content = yaml.dump(config, default_flow_style=False, indent=2, sort_keys=False)
                    await f.write(yaml_content)

                logger.info(f"[CLEANUP] Removed {removed_count} deprecated strategies from production")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup deprecated strategies: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [PUBLIC] PUBLIC API METHODS FOR EXTERNAL INTEGRATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def export_strategies_to_production(self, strategy_ids: List[str] = None, force: bool = False) -> bool:
        """🔄 Public method to export evolved strategies to production configuration

        Args:
            strategy_ids: List of specific strategy IDs to export (None for all eligible)
            force: If True, export strategies even if they don't meet production criteria

        Returns:
            bool: True if export was successful
        """
        try:
            logger.info(f"[API] Export request - IDs: {strategy_ids}, Force: {force}")

            if force and strategy_ids:
                # Force export specific strategies
                return await self._export_strategies_to_yaml(strategy_ids)
            else:
                # Use normal production sync logic
                return await self._sync_evolved_strategies_to_production()

        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies to production: {e}")
            return False

    async def get_evolved_strategies_summary(self) -> Dict[str, Any]:
        """📊 Get summary of evolved strategies and their performance"""
        try:
            summary = {
                'total_strategies': len(self.strategy_registry),
                'by_status': {},
                'top_performers': [],
                'ready_for_production': [],
                'recent_evolutions': len([e for e in self.evolution_history if
                                        (datetime.now() - e.timestamp).days <= 7])
            }

            # Count by status
            for strategy in self.strategy_registry.values():
                status = strategy.status.value
                summary['by_status'][status] = summary['by_status'].get(status, 0) + 1

            # Find top performers
            performance_scores = []
            for strategy_id, strategy in self.strategy_registry.items():
                if strategy_id in self.performance_history:
                    metrics_list = self.performance_history[strategy_id]
                    if metrics_list:
                        latest_metrics = metrics_list[-1]
                        score = (latest_metrics.roi * 0.4 +
                                latest_metrics.sharpe_ratio * 0.3 +
                                latest_metrics.win_rate * 0.3)
                        performance_scores.append({
                            'strategy_id': strategy_id,
                            'name': strategy.name,
                            'score': score,
                            'roi': latest_metrics.roi,
                            'sharpe': latest_metrics.sharpe_ratio,
                            'win_rate': latest_metrics.win_rate
                        })

            # Sort and get top 5
            performance_scores.sort(key=lambda x: x['score'], reverse=True)
            summary['top_performers'] = performance_scores[:5]

            # Find strategies ready for production
            for strategy_id, strategy in self.strategy_registry.items():
                if await self._is_strategy_ready_for_production(strategy_id, strategy):
                    summary['ready_for_production'].append({
                        'strategy_id': strategy_id,
                        'name': strategy.name,
                        'status': strategy.status.value
                    })

            return summary

        except Exception as e:
            logger.error(f"[ERROR] Failed to get evolved strategies summary: {e}")
            return {}

    async def promote_strategy_to_production(self, strategy_id: str, force: bool = False) -> bool:
        """⬆️ Manually promote a strategy to production"""
        try:
            if strategy_id not in self.strategy_registry:
                logger.error(f"[PROMOTE] Strategy {strategy_id} not found in registry")
                return False

            strategy = self.strategy_registry[strategy_id]

            # Check if strategy meets criteria (unless forced)
            if not force and not await self._is_strategy_ready_for_production(strategy_id, strategy):
                logger.warning(f"[PROMOTE] Strategy {strategy_id} does not meet production criteria")
                return False

            # Update status
            strategy.status = StrategyStatus.PROMOTED

            # Export to production configuration
            success = await self._export_strategies_to_yaml([strategy_id])

            if success:
                await self._save_strategy_registry()

                # Log promotion event
                await self._log_evolution_event(
                    strategy_id=strategy_id,
                    reason=EvolutionReason.MANUAL_REQUEST,
                    changes={'status': 'promoted'},
                    description=f"Manually promoted strategy {strategy.name} to production"
                )

                logger.info(f"[SUCCESS] Promoted strategy {strategy_id} to production")
                return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to promote strategy {strategy_id}: {e}")
            return False

    async def demote_strategy_from_production(self, strategy_id: str, reason: str = "performance_decline") -> bool:
        """⬇️ Demote a strategy from production use"""
        try:
            if strategy_id not in self.strategy_registry:
                logger.error(f"[DEMOTE] Strategy {strategy_id} not found in registry")
                return False

            strategy = self.strategy_registry[strategy_id]

            # Update status
            old_status = strategy.status
            strategy.status = StrategyStatus.DEMOTED

            # Remove from production configuration
            await self._remove_strategy_from_production_config(strategy_id)

            await self._save_strategy_registry()

            # Log demotion event
            await self._log_evolution_event(
                strategy_id=strategy_id,
                reason=EvolutionReason.UNDERPERFORMANCE,
                changes={'status': 'demoted', 'previous_status': old_status.value, 'reason': reason},
                description=f"Demoted strategy {strategy.name} from production due to {reason}"
            )

            logger.info(f"[SUCCESS] Demoted strategy {strategy_id} from production")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to demote strategy {strategy_id}: {e}")
            return False

    async def retire_strategy(self, strategy_id: str, reason: str = "obsolete") -> bool:
        """🗄️ Retire a strategy (mark as deprecated and archive)"""
        try:
            if strategy_id not in self.strategy_registry:
                logger.error(f"[RETIRE] Strategy {strategy_id} not found in registry")
                return False

            strategy = self.strategy_registry[strategy_id]

            # Archive strategy data
            await self._archive_strategy(strategy_id)

            # Update status
            old_status = strategy.status
            strategy.status = StrategyStatus.DEPRECATED

            # Remove from production if it was there
            if old_status == StrategyStatus.PROMOTED:
                await self._remove_strategy_from_production_config(strategy_id)

            await self._save_strategy_registry()

            # Log retirement event
            await self._log_evolution_event(
                strategy_id=strategy_id,
                reason=EvolutionReason.MANUAL_REQUEST,
                changes={'status': 'deprecated', 'previous_status': old_status.value, 'reason': reason},
                description=f"Retired strategy {strategy.name} - {reason}"
            )

            logger.info(f"[SUCCESS] Retired strategy {strategy_id}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to retire strategy {strategy_id}: {e}")
            return False

    async def _remove_strategy_from_production_config(self, strategy_id: str):
        """🗑️ Remove a strategy from the production configuration file"""
        try:
            yaml_config_path = Path("config/options_strategies.yaml")
            if not yaml_config_path.exists():
                return

            # Load current configuration
            async with aiofiles.open(yaml_config_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                config = yaml.safe_load(content) or {}

            if 'strategies' not in config:
                return

            # Find and remove the strategy
            strategy = self.strategy_registry.get(strategy_id)
            if not strategy:
                return

            # Look for strategy by name pattern
            strategy_name_pattern = strategy.name.lower().replace(' ', '_')
            keys_to_remove = []

            for key in config['strategies'].keys():
                if strategy_name_pattern in key.lower() or 'evolved' in key.lower():
                    # Check metadata to confirm it's the right strategy
                    strategy_config = config['strategies'][key]
                    metadata = strategy_config.get('_performance_metadata', {})
                    if metadata.get('parent_strategy') == strategy.parent_id:
                        keys_to_remove.append(key)

            # Remove found strategies
            for key in keys_to_remove:
                del config['strategies'][key]
                logger.info(f"[REMOVE] Removed strategy {key} from production config")

            # Save updated configuration
            if keys_to_remove:
                async with aiofiles.open(yaml_config_path, 'w', encoding='utf-8') as f:
                    yaml_content = yaml.dump(config, default_flow_style=False, indent=2, sort_keys=False)
                    await f.write(yaml_content)

        except Exception as e:
            logger.error(f"[ERROR] Failed to remove strategy from production config: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [ENSEMBLE] FEATURE 6: META-STRATEGY FUSION (ENSEMBLE)
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _create_ensemble_strategies(self):
        """[ENSEMBLE] Create ensemble strategies from high-performing strategies"""
        while self.is_running:
            try:
                logger.info("[ENSEMBLE] [ENSEMBLE] Creating ensemble strategies...")

                # Find high-performing strategies
                top_performers = await self._identify_top_performers()

                if len(top_performers) >= 2:
                    # Create ensemble combinations
                    ensembles = await self._create_ensemble_combinations(top_performers)

                    for ensemble in ensembles:
                        self.strategy_registry[ensemble.strategy_id] = ensemble

                    if ensembles:
                        await self._save_strategy_registry()
                        logger.info(f"[ENSEMBLE] [ENSEMBLE] Created {len(ensembles)} ensemble strategies")

                await asyncio.sleep(self.intervals['full_evolution'] * 2)  # Less frequent

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Ensemble creation failed: {e}")
                await asyncio.sleep(600)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [EXPERIMENT] FEATURE 9: CONTINUOUS EXPERIMENTATION FRAMEWORK
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _continuous_experimentation(self):
        """[EXPERIMENT] Continuous A/B testing of experimental strategies"""
        while self.is_running:
            try:
                logger.info("[EXPERIMENT] [EXPERIMENT] Managing continuous experiments...")

                # Manage active experiments
                await self._manage_active_experiments()

                # Start new experiments
                await self._start_new_experiments()

                # Evaluate experiment results
                await self._evaluate_experiments()

                await asyncio.sleep(self.intervals['diversity_maintenance'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Continuous experimentation failed: {e}")
                await asyncio.sleep(300)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [CYCLE] FEATURE 10: SELF-LEARNING FEEDBACK LOOP
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _self_learning_loop(self):
        """[CYCLE] Self-learning feedback loop for adaptive optimization"""
        while self.is_running:
            try:
                logger.info("[CYCLE] [LEARNING] Running self-learning feedback loop...")

                # Analyze performance patterns
                await self._analyze_performance_patterns()

                # Update mutation parameters based on learning
                await self._update_mutation_parameters()

                # Adjust thresholds based on market conditions
                await self._adjust_performance_thresholds()

                await asyncio.sleep(self.intervals['full_evolution'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Self-learning loop failed: {e}")
                await asyncio.sleep(300)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [REGISTRY] FEATURE 12: STRATEGY VERSION REGISTRY
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _maintain_registry(self):
        """[REGISTRY] Maintain strategy version registry"""
        while self.is_running:
            try:
                logger.info("[REGISTRY] [REGISTRY] Maintaining strategy registry...")

                # Update strategy metadata
                await self._update_strategy_metadata()

                # Generate registry reports
                await self._generate_registry_reports()

                # Cleanup old versions
                await self._cleanup_old_versions()

                await asyncio.sleep(self.intervals['registry_cleanup'])

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Registry maintenance failed: {e}")
                await asyncio.sleep(300)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [LOGS] FEATURE 11: HUMAN-READABLE EVOLUTION LOGS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _generate_evolution_logs(self):
        """[LOGS] Generate human-readable evolution logs"""
        while self.is_running:
            try:
                logger.info("[LOGS] [LOGS] Generating evolution logs...")

                # Generate daily summary
                await self._generate_daily_summary()

                # Generate strategy evolution reports
                await self._generate_strategy_reports()

                # Generate performance insights
                await self._generate_performance_insights()

                await asyncio.sleep(86400)  # Daily

            except Exception as e:
                logger.error(f"[ERROR] [ERROR] Evolution log generation failed: {e}")
                await asyncio.sleep(3600)  # Retry in 1 hour

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔧 UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _detect_current_market_regime(self) -> MarketRegime:
        """[REGIME] Detect current market regime"""
        try:
            # Load market data
            market_data_file = self.data_path / "live" / "market_summary.json"
            if market_data_file.exists():
                async with aiofiles.open(market_data_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    market_data = json.loads(content)

                return await self._detect_market_regime(market_data)
            else:
                return MarketRegime.SIDEWAYS_LOW_VOL

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to detect market regime: {e}")
            return MarketRegime.SIDEWAYS_LOW_VOL

    async def _detect_market_regime(self, market_data: Dict) -> MarketRegime:
        """[REGIME] Detect market regime from market data"""
        try:
            # Extract key metrics
            volatility = market_data.get('volatility', 0.15)
            trend_strength = market_data.get('trend_strength', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            price_change_1d = market_data.get('price_change_1d', 0)

            # Regime detection logic
            if abs(trend_strength) > 0.7:
                if trend_strength > 0:
                    return MarketRegime.TRENDING_BULL
                else:
                    return MarketRegime.TRENDING_BEAR
            elif volatility > 0.25:
                if volume_ratio > 1.5:
                    return MarketRegime.VOLATILE_UNCERTAIN
                else:
                    return MarketRegime.SIDEWAYS_HIGH_VOL
            elif abs(price_change_1d) > 0.03:
                return MarketRegime.BREAKOUT
            else:
                return MarketRegime.SIDEWAYS_LOW_VOL

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to detect market regime: {e}")
            return MarketRegime.SIDEWAYS_LOW_VOL

    async def _send_notification(self, title: str, message: str):
        """[EMAIL] Send notification via email/telegram"""
        try:
            # Email notification
            if self.notifications.get('email_enabled', False):
                await self._send_email_notification(title, message)

            # Telegram notification
            if self.notifications.get('telegram_enabled', False):
                await self._send_telegram_notification(title, message)

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to send notification: {e}")

    async def _send_email_notification(self, title: str, message: str):
        """[EMAIL] Send email notification"""
        try:
            email_config = self.notifications.get('email_config') or {}

            if not email_config.get('username') or not email_config.get('recipients'):
                return

            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"[GENETIC] Strategy Evolution: {title}"

            body = f"""
            {title}

            {message}

            Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            --
            Options Strategy Evolution Agent
            """

            msg.attach(MIMEText(body, 'plain'))

            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()

            logger.info(f"[EMAIL] [EMAIL] Notification sent: {title}")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to send email notification: {e}")

    async def _send_telegram_notification(self, title: str, message: str):
        """📱 Send telegram notification"""
        try:
            telegram_config = self.notifications.get('telegram_config') or {}

            if not telegram_config.get('bot_token') or not telegram_config.get('chat_ids'):
                return

            bot_token = telegram_config['bot_token']
            chat_ids = telegram_config['chat_ids']

            text = f"[GENETIC] *Strategy Evolution*\n\n*{title}*\n\n{message}"

            async with aiohttp.ClientSession() as session:
                for chat_id in chat_ids:
                    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                    data = {
                        'chat_id': chat_id,
                        'text': text,
                        'parse_mode': 'Markdown'
                    }

                    async with session.post(url, data=data) as response:
                        if response.status == 200:
                            logger.info(f"📱 [TELEGRAM] Notification sent to {chat_id}")
                        else:
                            logger.error(f"[ERROR] [TELEGRAM] Failed to send to {chat_id}: {response.status}")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to send telegram notification: {e}")

    async def _log_evolution_event(self, strategy_id: str, reason: EvolutionReason,
                                 changes: Dict[str, Any], description: str,
                                 metrics_after: Optional[StrategyMetrics] = None):
        """[LOGS] Log evolution event"""
        try:
            # Get current metrics as "before"
            metrics_before = None
            if strategy_id in self.performance_history and self.performance_history[strategy_id]:
                metrics_before = self.performance_history[strategy_id][-1]
            else:
                # Create dummy metrics if none exist
                metrics_before = StrategyMetrics(
                    strategy_id=strategy_id,
                    roi=0, sharpe_ratio=0, win_rate=0, max_drawdown=0,
                    expectancy=0, profit_factor=1, total_trades=0,
                    avg_trade_duration=0, volatility=0, calmar_ratio=0,
                    sortino_ratio=0, timestamp=datetime.now(),
                    regime=self.market_regime_cache or MarketRegime.SIDEWAYS_LOW_VOL
                )

            strategy = self.strategy_registry.get(strategy_id)
            parent_id = strategy.parent_id if strategy else None

            event = EvolutionEvent(
                event_id=f"evt_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}",
                strategy_id=strategy_id,
                parent_id=parent_id,
                reason=reason,
                changes=changes,
                metrics_before=metrics_before,
                metrics_after=metrics_after,
                timestamp=datetime.now(),
                description=description
            )

            self.evolution_history.append(event)

            # Save to file periodically
            if len(self.evolution_history) % 10 == 0:
                await self._save_evolution_history()

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to log evolution event: {e}")

    async def _archive_strategy(self, strategy_id: str):
        """🗄️ Archive strategy data"""
        try:
            archive_path = self.evolution_path / "archive"
            archive_path.mkdir(exist_ok=True)

            # Archive strategy config
            if strategy_id in self.strategy_registry:
                strategy_data = self.strategy_registry[strategy_id].to_dict()

                archive_file = archive_path / f"strategy_{strategy_id}_{datetime.now().strftime('%Y%m%d')}.json"
                async with aiofiles.open(archive_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(strategy_data, indent=2, default=str))

            # Archive performance history
            if strategy_id in self.performance_history:
                performance_data = [m.to_dict() for m in self.performance_history[strategy_id]]

                perf_archive_file = archive_path / f"performance_{strategy_id}_{datetime.now().strftime('%Y%m%d')}.json"
                async with aiofiles.open(perf_archive_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(performance_data, indent=2, default=str))

                # Remove from active history
                del self.performance_history[strategy_id]

            logger.info(f"🗄️ [ARCHIVE] Strategy {strategy_id} archived successfully")

        except Exception as e:
            logger.error(f"[ERROR] [ERROR] Failed to archive strategy {strategy_id}: {e}")

    # Placeholder methods for features that need more implementation
    async def _adapt_strategies_to_regime(self, current_regime: MarketRegime, previous_regime: MarketRegime):
        """[REGIME] Adapt strategies to new market regime"""
        logger.info(f"Adapting strategies to new regime: {current_regime.value}")
        for strategy in self.strategy_registry.values():
            # Adjust risk parameters based on volatility
            if 'high_vol' in current_regime.value:
                strategy.risk_management['stop_loss'] = strategy.risk_management.get('stop_loss', 0.05) * 1.2
            else:
                strategy.risk_management['stop_loss'] = strategy.risk_management.get('stop_loss', 0.05) / 1.2
        logger.info("Finished adapting strategies to new regime.")

    async def _identify_top_performers(self) -> List[StrategyConfig]:
        """[ENSEMBLE] Identify top performing strategies"""
        performance_scores = []
        for strategy_id, history in self.performance_history.items():
            if history:
                latest_metrics = history[-1]
                score = (latest_metrics.sharpe_ratio * 0.5) + (latest_metrics.roi * 0.3) + (latest_metrics.win_rate * 0.2)
                performance_scores.append((score, strategy_id))
        
        performance_scores.sort(key=lambda x: x[0], reverse=True)
        
        top_performers_ids = [sid for score, sid in performance_scores[:10]]
        return [self.strategy_registry[sid] for sid in top_performers_ids if sid in self.strategy_registry]

    async def _create_ensemble_combinations(self, top_performers: List[StrategyConfig]) -> List[StrategyConfig]:
        """[ENSEMBLE] Create ensemble strategy combinations"""
        ensembles = []
        if len(top_performers) < 2:
            return ensembles

        # Simple ensemble: combine entry signals of top 2 performers
        parent1, parent2 = top_performers[0], top_performers[1]
        
        ensemble_id = f"ensemble_{parent1.strategy_id[:5]}_{parent2.strategy_id[:5]}_{datetime.now().strftime('%Y%m%d')}"
        
        ensemble_config = StrategyConfig(
            strategy_id=ensemble_id,
            name=f"Ensemble of {parent1.name} and {parent2.name}",
            description="Combines entry signals from two top-performing strategies.",
            parameters={},
            entry_conditions=[f"({c1}) and ({c2})" for c1 in parent1.entry_conditions for c2 in parent2.entry_conditions],
            exit_conditions=list(set(parent1.exit_conditions + parent2.exit_conditions)),
            risk_management=parent1.risk_management, # or average them
            market_outlook='neutral',
            volatility_outlook='neutral',
            timeframe=parent1.timeframe,
            status=StrategyStatus.EXPERIMENTAL,
            tags=['ensemble']
        )
        ensembles.append(ensemble_config)
        return ensembles

    async def _manage_active_experiments(self):
        """[EXPERIMENT] Manage active experiments"""
        # This is a placeholder for more complex experiment management.
        # For now, we just log the number of experimental strategies.
        experimental_count = sum(1 for s in self.strategy_registry.values() if s.status == StrategyStatus.EXPERIMENTAL)
        logger.info(f"Managing {experimental_count} active experiments.")

    async def _start_new_experiments(self):
        """[EXPERIMENT] Start new experiments"""
        # A/B test: pit a new mutation against its parent
        experimental_strategies = [s for s in self.strategy_registry.values() if s.status == StrategyStatus.EXPERIMENTAL and s.parent_id]
        if experimental_strategies:
            candidate = random.choice(experimental_strategies)
            parent_id = candidate.parent_id
            if parent_id in self.strategy_registry:
                logger.info(f"Starting new A/B test between {candidate.strategy_id} and parent {parent_id}.")
                self.active_experiments[candidate.strategy_id] = {'parent': parent_id, 'start_date': datetime.now()}

    async def _evaluate_experiments(self):
        """[EXPERIMENT] Evaluate experiment results"""
        for exp_id, exp_data in list(self.active_experiments.items()):
            if (datetime.now() - exp_data['start_date']).days > 7: # Run experiment for 7 days
                parent_id = exp_data['parent']
                
                exp_perf = self.performance_history.get(exp_id, [])
                parent_perf = self.performance_history.get(parent_id, [])

                if exp_perf and parent_perf:
                    exp_score = exp_perf[-1].sharpe_ratio
                    parent_score = parent_perf[-1].sharpe_ratio

                    if exp_score > parent_score:
                        self.strategy_registry[parent_id].status = StrategyStatus.DEPRECATED
                        self.strategy_registry[exp_id].status = StrategyStatus.PROMOTED
                        logger.info(f"Experiment winner: {exp_id} promoted, parent {parent_id} deprecated.")
                    else:
                        self.strategy_registry[exp_id].status = StrategyStatus.DISABLED
                        logger.info(f"Experiment loser: {exp_id} disabled.")
                
                del self.active_experiments[exp_id]

    async def _analyze_performance_patterns(self):
        """[CYCLE] Analyze performance patterns"""
        # Analyze which mutation approaches work best
        successful_mutations = [s for s in self.strategy_registry.values() if s.status == StrategyStatus.PROMOTED and s.parent_id]
        mutation_success = defaultdict(int)
        for s in successful_mutations:
            if 'conservative_mutation' in s.tags:
                mutation_success['conservative'] += 1
            if 'aggressive_mutation' in s.tags:
                mutation_success['aggressive'] += 1
            if 'targeted_mutation' in s.tags:
                mutation_success['targeted'] += 1
        
        if mutation_success:
            logger.info(f"Mutation success rates: {dict(mutation_success)}")

    async def _update_mutation_parameters(self):
        """[CYCLE] Update mutation parameters based on learning"""
        # If aggressive mutations are working well, increase mutation rate
        # This is a simplified example.
        # A more advanced implementation would analyze which parameters are most effective to change.
        logger.info("Updating mutation parameters based on learning.")
        self.mutation_rate = min(0.3, self.mutation_rate * 1.05) # Slowly increase mutation rate

    async def _adjust_performance_thresholds(self):
        """[CYCLE] Adjust performance thresholds"""
        # If market is volatile, be more tolerant to drawdowns
        if self.market_regime_cache and 'high_vol' in self.market_regime_cache.value:
            self.performance_thresholds['max_drawdown'] = 0.20
        else:
            self.performance_thresholds['max_drawdown'] = 0.15
        logger.info(f"Adjusted max drawdown threshold to {self.performance_thresholds['max_drawdown']}")

    async def _update_strategy_metadata(self):
        """[REGISTRY] Update strategy metadata"""
        for strategy_id, history in self.performance_history.items():
            if strategy_id in self.strategy_registry:
                regime_performance = defaultdict(list)
                for metric in history:
                    regime_performance[metric.regime].append(metric.sharpe_ratio)
                
                avg_regime_perf = {r: statistics.mean(p) for r, p in regime_performance.items()}
                if avg_regime_perf:
                    best_regime = max(avg_regime_perf, key=avg_regime_perf.get)
                    worst_regime = min(avg_regime_perf, key=avg_regime_perf.get)
                    self.strategy_registry[strategy_id].best_regime = best_regime
                    self.strategy_registry[strategy_id].worst_regime = worst_regime

    async def _generate_registry_reports(self):
        """[REGISTRY] Generate registry reports"""
        report = {
            "total_strategies": len(self.strategy_registry),
            "active_strategies": sum(1 for s in self.strategy_registry.values() if s.status == StrategyStatus.ACTIVE),
            "experimental_strategies": sum(1 for s in self.strategy_registry.values() if s.status == StrategyStatus.EXPERIMENTAL),
            "deprecated_strategies": sum(1 for s in self.strategy_registry.values() if s.status == StrategyStatus.DEPRECATED),
        }
        report_path = self.logs_path / f"registry_report_{datetime.now().strftime('%Y%m%d')}.json"
        async with aiofiles.open(report_path, 'w') as f:
            await f.write(json.dumps(report, indent=2))
        logger.info("Generated daily registry report.")

    async def _cleanup_old_versions(self):
        """[REGISTRY] Cleanup old strategy versions"""
        # Remove disabled strategies older than 30 days
        cutoff = datetime.now() - timedelta(days=30)
        to_delete = [sid for sid, s in self.strategy_registry.items() if s.status == StrategyStatus.DISABLED and s.created_at < cutoff]
        
        for sid in to_delete:
            del self.strategy_registry[sid]
            if sid in self.performance_history:
                del self.performance_history[sid]
        
        if to_delete:
            logger.info(f"Cleaned up {len(to_delete)} old disabled strategies.")

    async def _generate_daily_summary(self):
        """[LOGS] Generate daily evolution summary"""
        today = datetime.now().date()
        events_today = [e for e in self.evolution_history if e.timestamp.date() == today]
        
        summary = f"Daily Evolution Summary for {today}:\n"
        summary += f"- Total evolution events: {len(events_today)}\n"
        promotions = sum(1 for e in events_today if 'promote' in e.description.lower())
        summary += f"- Strategies promoted: {promotions}\n"
        
        summary_path = self.logs_path / f"summary_{today.strftime('%Y%m%d')}.log"
        async with aiofiles.open(summary_path, 'w') as f:
            await f.write(summary)
        logger.info("Generated daily evolution summary.")

    async def _generate_strategy_reports(self):
        """[LOGS] Generate strategy evolution reports"""
        # Report on the lineage of a top-performing strategy
        top_performers = await self._identify_top_performers()
        if top_performers:
            focus_strategy = top_performers[0]
            report = f"Evolution Report for {focus_strategy.name} ({focus_strategy.strategy_id})\n"
            
            curr = focus_strategy
            lineage = []
            while curr and curr.parent_id and curr.parent_id in self.strategy_registry:
                lineage.append(curr.parent_id)
                # This logic is simplified. A real implementation would handle crossover parents.
                parent_ids = curr.parent_id.split('+')
                curr = self.strategy_registry.get(parent_ids[0])

            report += f"Lineage: {' -> '.join(reversed(lineage))}\n"
            
            report_path = self.logs_path / f"strategy_report_{focus_strategy.strategy_id}.log"
            async with aiofiles.open(report_path, 'w') as f:
                await f.write(report)
            logger.info(f"Generated report for strategy {focus_strategy.strategy_id}")

    async def _generate_performance_insights(self):
        """[LOGS] Generate performance insights"""
        # Find common parameters in top-performing strategies
        top_performers = await self._identify_top_performers()
        if top_performers:
            param_counts = defaultdict(list)
            for s in top_performers:
                for p, v in s.parameters.items():
                    param_counts[p].append(v)
            
            insights = "Performance Insights:\n"
            for p, values in param_counts.items():
                if all(isinstance(v, (int, float)) for v in values):
                    insights += f"- Top performer average for '{p}': {statistics.mean(values):.2f}\n"
            
            insights_path = self.logs_path / f"insights_{datetime.now().strftime('%Y%m%d')}.log"
            async with aiofiles.open(insights_path, 'w') as f:
                await f.write(insights)
            logger.info("Generated performance insights.")

# Example usage
async def main():
    """🚀 Main entry point for Strategy Evolution Agent"""
    agent = OptionsStrategyEvolutionAgent()
    try:
        logger.info("[GENETIC] Starting Options Strategy Evolution Agent...")
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("🛑 Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
